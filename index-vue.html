<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级爬虫选择器调试工具 - Vue版</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Monaco Editor -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.34.1/min/vs/loader.js"></script>
    <style>
        body { 
            margin: 0; 
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif; 
        }
        .main-container { 
            height: 100vh; 
            display: flex; 
            flex-direction: column; 
        }
        .content-container { 
            flex: 1; 
            display: flex; 
            overflow: hidden; 
        }
        .left-panel, .center-panel, .right-panel { 
            display: flex; 
            flex-direction: column; 
            background: #fff;
        }
        .left-panel { 
            width: 380px; 
            border-right: 1px solid #e4e7ed; 
        }
        .center-panel { 
            flex: 1; 
            border-right: 1px solid #e4e7ed; 
        }
        .right-panel { 
            width: 450px; 
        }
        .panel-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e4e7ed;
            background: #fafafa;
            font-weight: 600;
            color: #303133;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .panel-content { 
            flex: 1; 
            padding: 20px; 
            overflow-y: auto; 
        }
        .monaco-container { 
            height: 350px; 
            border: 1px solid #dcdfe6; 
            border-radius: 4px; 
            margin-top: 12px;
        }
        .field-card { 
            margin-bottom: 16px; 
        }
        .project-tabs { 
            background: #f5f7fa; 
            border-bottom: 1px solid #e4e7ed;
            padding: 0 20px;
            display: flex;
            align-items: center;
            min-height: 48px;
        }
        .results-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .stats-card {
            margin-bottom: 16px;
        }
        .el-form-item {
            margin-bottom: 18px;
        }
        .drag-handle {
            cursor: move;
            color: #909399;
            margin-right: 8px;
        }
        .drag-handle:hover {
            color: #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="main-container">
            <!-- 顶部导航栏 -->
            <el-header style="padding: 0; height: 60px;">
                <div style="height: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <h2 style="margin: 0; font-size: 18px; font-weight: 600;">
                            <el-icon style="margin-right: 8px;"><Tools /></el-icon>
                            高级爬虫选择器调试工具
                        </h2>
                        <el-tag v-if="currentProject" type="info" effect="plain" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                            {{ currentProject.name }} ({{ currentProject.fields.length }} 字段)
                        </el-tag>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <el-button type="primary" :icon="Save" @click="saveProject" size="small" plain>保存项目</el-button>
                        <el-button type="primary" :icon="FolderOpened" @click="loadProject" size="small" plain>加载项目</el-button>
                        <el-button type="warning" :icon="RefreshLeft" @click="undoAction" size="small" plain :disabled="!canUndo">撤销</el-button>
                        <el-switch v-model="isDarkMode" @change="toggleTheme" 
                                   active-text="深色" inactive-text="浅色" 
                                   style="--el-switch-on-color: #409eff; --el-switch-off-color: #dcdfe6;">
                        </el-switch>
                    </div>
                </div>
            </el-header>

            <!-- 项目标签栏 -->
            <div class="project-tabs">
                <el-tabs v-model="currentProjectIndex" @tab-click="switchProject" @tab-remove="closeProject" type="card" closable>
                    <el-tab-pane 
                        v-for="(project, index) in projects" 
                        :key="index"
                        :label="project.name"
                        :name="index.toString()"
                    >
                        <template #label>
                            <span style="display: flex; align-items: center; gap: 6px;">
                                <el-icon><Document /></el-icon>
                                {{ project.name }}
                                <el-badge v-if="project.fields.length > 0" :value="project.fields.length" type="primary" />
                                <el-icon v-if="project.hasUnsavedChanges" style="color: #e6a23c;"><Warning /></el-icon>
                            </span>
                        </template>
                    </el-tab-pane>
                </el-tabs>
                <el-button type="primary" :icon="Plus" @click="createNewProject" size="small" style="margin-left: 12px;">新建项目</el-button>
            </div>

            <!-- 主要内容区域 -->
            <div class="content-container">
                <!-- 左侧配置面板 -->
                <div class="left-panel">
                    <div class="panel-header">
                        <el-icon><Setting /></el-icon>
                        配置面板
                    </div>
                    <div class="panel-content">
                        <!-- HTML输入 -->
                        <el-form label-position="top">
                            <el-form-item label="HTML代码">
                                <el-input 
                                    v-model="currentProject.html"
                                    type="textarea" 
                                    :rows="8"
                                    placeholder="请输入要解析的HTML代码..."
                                    @input="onHtmlChange"
                                />
                            </el-form-item>

                            <!-- 列表项选择器 -->
                            <el-form-item>
                                <template #label>
                                    <span style="display: flex; align-items: center; gap: 8px;">
                                        列表项选择器
                                        <el-button type="primary" :icon="QuestionFilled" @click="showReference" size="small" text>参考手册</el-button>
                                        <el-button type="success" :icon="MagicStick" @click="suggestSelectors" size="small" text>智能建议</el-button>
                                    </span>
                                </template>
                                <el-input 
                                    v-model="currentProject.selector"
                                    placeholder="例如: .product-card"
                                    @input="onSelectorChange"
                                >
                                    <template #prefix>
                                        <el-icon><Search /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>

                            <!-- 字段配置 -->
                            <el-form-item label="提取字段">
                                <div style="margin-bottom: 12px;">
                                    <el-button type="primary" :icon="Plus" @click="addField" size="small">添加字段</el-button>
                                    <el-button-group style="margin-left: 12px;">
                                        <el-button :icon="Document" @click="addPresetField('title')" size="small">标题</el-button>
                                        <el-button :icon="Link" @click="addPresetField('link')" size="small">链接</el-button>
                                        <el-button :icon="Picture" @click="addPresetField('image')" size="small">图片</el-button>
                                        <el-button :icon="PriceTag" @click="addPresetField('price')" size="small">价格</el-button>
                                    </el-button-group>
                                </div>

                                <!-- 字段列表 -->
                                <div v-if="currentProject.fields.length === 0" style="text-align: center; color: #909399; padding: 40px 0;">
                                    <el-icon size="48" style="margin-bottom: 16px;"><Box /></el-icon>
                                    <p>暂无字段，点击上方按钮添加</p>
                                </div>

                                <el-card v-for="(field, index) in currentProject.fields" :key="index" class="field-card" shadow="hover">
                                    <div style="display: flex; align-items: flex-start; gap: 12px;">
                                        <el-icon class="drag-handle"><Rank /></el-icon>
                                        <div style="flex: 1;">
                                            <el-row :gutter="12">
                                                <el-col :span="12">
                                                    <el-form-item label="字段名" size="small">
                                                        <el-input v-model="field.name" placeholder="例如: title" @input="onFieldChange" />
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-form-item label="提取类型" size="small">
                                                        <el-select v-model="field.type" @change="onFieldChange">
                                                            <el-option label="文本" value="text" />
                                                            <el-option label="属性" value="attribute" />
                                                        </el-select>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                            <el-row :gutter="12">
                                                <el-col :span="field.type === 'attribute' ? 12 : 24">
                                                    <el-form-item label="子选择器" size="small">
                                                        <el-input v-model="field.selector" placeholder="例如: h2.name" @input="onFieldChange" />
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12" v-if="field.type === 'attribute'">
                                                    <el-form-item label="属性名" size="small">
                                                        <el-input v-model="field.attribute" placeholder="例如: href" @input="onFieldChange" />
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                        </div>
                                        <el-button type="danger" :icon="Delete" @click="removeField(index)" size="small" circle />
                                    </div>
                                </el-card>
                            </el-form-item>

                            <!-- 测试按钮 -->
                            <el-button type="success" :icon="Search" @click="testSelector" style="width: 100%;" size="large">
                                测试选择器
                            </el-button>
                        </el-form>
                    </div>
                </div>

                <!-- 中间结果面板 -->
                <div class="center-panel">
                    <div class="panel-header">
                        <el-icon><DataAnalysis /></el-icon>
                        匹配结果
                    </div>
                    <div class="panel-content">
                        <!-- 统计信息 -->
                        <el-card class="stats-card" v-if="results.length > 0">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; gap: 24px;">
                                    <div style="text-align: center;">
                                        <div style="font-size: 24px; font-weight: bold; color: #409eff;">{{ results.length }}</div>
                                        <div style="color: #909399; font-size: 12px;">匹配项</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 24px; font-weight: bold; color: #67c23a;">{{ currentProject.fields.length }}</div>
                                        <div style="color: #909399; font-size: 12px;">字段数</div>
                                    </div>
                                </div>
                                <el-button type="primary" :icon="Download" @click="exportResults" size="small">导出数据</el-button>
                            </div>
                        </el-card>

                        <!-- 结果表格 -->
                        <div class="results-container">
                            <el-empty v-if="results.length === 0" description="暂无匹配结果">
                                <template #image>
                                    <el-icon size="60" style="color: #c0c4cc;"><Search /></el-icon>
                                </template>
                            </el-empty>

                            <el-table v-else :data="results" stripe style="width: 100%;" size="small">
                                <el-table-column 
                                    v-for="field in currentProject.fields" 
                                    :key="field.name"
                                    :prop="field.name" 
                                    :label="field.name"
                                    show-overflow-tooltip
                                />
                            </el-table>
                        </div>
                    </div>
                </div>

                <!-- 右侧代码面板 -->
                <div class="right-panel">
                    <div class="panel-header">
                        <el-icon><Code /></el-icon>
                        生成代码
                        <div style="margin-left: auto; display: flex; gap: 8px;">
                            <el-select v-model="codeLanguage" size="small" style="width: 120px;">
                                <el-option label="Go" value="go" />
                                <el-option label="Python" value="python" />
                                <el-option label="JavaScript" value="javascript" />
                            </el-select>
                            <el-button type="primary" :icon="DocumentCopy" @click="copyCode" size="small">复制</el-button>
                            <el-button type="success" :icon="Download" @click="exportCode" size="small">导出</el-button>
                        </div>
                    </div>
                    <div class="panel-content">
                        <div class="monaco-container" ref="monacoContainer"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 隐藏的文件输入 -->
        <input type="file" ref="fileInput" @change="handleFileLoad" accept=".json" style="display: none;" />

        <!-- CSS选择器参考手册对话框 -->
        <el-dialog v-model="showReferenceDialog" title="CSS选择器参考手册" width="80%" :before-close="closeReference">
            <div style="max-height: 60vh; overflow-y: auto;">
                <!-- 这里可以放置参考手册内容 -->
                <el-collapse>
                    <el-collapse-item title="基础选择器" name="basic">
                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="元素选择器">
                                <code>div</code> - 选择所有div元素
                                <el-button type="primary" size="small" @click="trySelector('div')" style="margin-left: 12px;">试用</el-button>
                            </el-descriptions-item>
                            <el-descriptions-item label="类选择器">
                                <code>.class-name</code> - 选择指定类名的元素
                                <el-button type="primary" size="small" @click="trySelector('.product-card')" style="margin-left: 12px;">试用</el-button>
                            </el-descriptions-item>
                            <el-descriptions-item label="ID选择器">
                                <code>#id-name</code> - 选择指定ID的元素
                                <el-button type="primary" size="small" @click="trySelector('#header')" style="margin-left: 12px;">试用</el-button>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                    <el-collapse-item title="属性选择器" name="attribute">
                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="存在属性">
                                <code>[attr]</code> - 选择具有指定属性的元素
                                <el-button type="primary" size="small" @click="trySelector('[data-id]')" style="margin-left: 12px;">试用</el-button>
                            </el-descriptions-item>
                            <el-descriptions-item label="属性值匹配">
                                <code>[attr=value]</code> - 选择属性值完全匹配的元素
                                <el-button type="primary" size="small" @click="trySelector('[type=\"text\"]')" style="margin-left: 12px;">试用</el-button>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-dialog>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive, computed, watch, onMounted, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 注册所有图标
        const icons = ElementPlusIconsVue;

        const app = createApp({
            setup() {
                // 响应式数据
                const isDarkMode = ref(false);
                const currentProjectIndex = ref('0');
                const codeLanguage = ref('go');
                const showReferenceDialog = ref(false);
                const canUndo = ref(false);
                const results = ref([]);
                const monacoEditor = ref(null);

                // 项目数据
                const projects = ref([
                    {
                        name: '🚀 主项目',
                        html: '',
                        selector: '.product-card',
                        fields: [
                            { name: 'title', selector: 'h2.product-title', type: 'text', attribute: '' },
                            { name: 'price', selector: '.price', type: 'text', attribute: '' },
                            { name: 'url', selector: 'a', type: 'attribute', attribute: 'href' }
                        ],
                        hasUnsavedChanges: false
                    }
                ]);

                // 计算属性
                const currentProject = computed(() => {
                    const index = parseInt(currentProjectIndex.value);
                    return projects.value[index] || projects.value[0];
                });

                // 方法
                const toggleTheme = () => {
                    document.documentElement.classList.toggle('dark', isDarkMode.value);
                };

                const createNewProject = () => {
                    const newProject = {
                        name: `📄 项目 ${projects.value.length + 1}`,
                        html: '',
                        selector: '',
                        fields: [],
                        hasUnsavedChanges: false
                    };
                    projects.value.push(newProject);
                    currentProjectIndex.value = (projects.value.length - 1).toString();
                    ElMessage.success('新项目创建成功');
                };

                const switchProject = (tab) => {
                    currentProjectIndex.value = tab.name;
                };

                const closeProject = (name) => {
                    const index = parseInt(name);
                    if (projects.value.length <= 1) {
                        ElMessage.warning('至少需要保留一个项目');
                        return;
                    }
                    projects.value.splice(index, 1);
                    if (parseInt(currentProjectIndex.value) >= projects.value.length) {
                        currentProjectIndex.value = (projects.value.length - 1).toString();
                    }
                    ElMessage.success('项目已关闭');
                };

                const addField = () => {
                    currentProject.value.fields.push({
                        name: '',
                        selector: '',
                        type: 'text',
                        attribute: ''
                    });
                    markAsChanged();
                };

                const addPresetField = (type) => {
                    const presets = {
                        title: { name: 'title', selector: 'h1, h2, h3', type: 'text' },
                        link: { name: 'link', selector: 'a', type: 'attribute', attribute: 'href' },
                        image: { name: 'image', selector: 'img', type: 'attribute', attribute: 'src' },
                        price: { name: 'price', selector: '.price, [class*=price]', type: 'text' }
                    };
                    const preset = presets[type];
                    if (preset) {
                        currentProject.value.fields.push({ ...preset, attribute: preset.attribute || '' });
                        markAsChanged();
                        ElMessage.success(`${preset.name} 字段已添加`);
                    }
                };

                const removeField = (index) => {
                    currentProject.value.fields.splice(index, 1);
                    markAsChanged();
                };

                const markAsChanged = () => {
                    currentProject.value.hasUnsavedChanges = true;
                };

                const onHtmlChange = () => {
                    markAsChanged();
                    testSelector();
                };

                const onSelectorChange = () => {
                    markAsChanged();
                    testSelector();
                };

                const onFieldChange = () => {
                    markAsChanged();
                    testSelector();
                };

                const testSelector = () => {
                    if (!currentProject.value.html || !currentProject.value.selector) {
                        results.value = [];
                        return;
                    }

                    try {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(currentProject.value.html, 'text/html');
                        const elements = doc.querySelectorAll(currentProject.value.selector);

                        const extractedData = [];
                        elements.forEach(element => {
                            const item = {};
                            currentProject.value.fields.forEach(field => {
                                if (field.name && field.selector) {
                                    const targetElement = element.querySelector(field.selector);
                                    if (targetElement) {
                                        if (field.type === 'attribute' && field.attribute) {
                                            item[field.name] = targetElement.getAttribute(field.attribute) || '';
                                        } else {
                                            item[field.name] = targetElement.textContent.trim();
                                        }
                                    } else {
                                        item[field.name] = '';
                                    }
                                }
                            });
                            extractedData.push(item);
                        });

                        results.value = extractedData;
                        generateCode();
                    } catch (error) {
                        console.error('解析错误:', error);
                        ElMessage.error('HTML解析失败');
                        results.value = [];
                    }
                };

                const generateCode = () => {
                    if (!monacoEditor.value) return;

                    const selector = currentProject.value.selector;
                    const fields = currentProject.value.fields;

                    if (!selector || fields.length === 0) {
                        monacoEditor.value.setValue('// 请配置选择器和字段后生成代码');
                        return;
                    }

                    let code = '';
                    if (codeLanguage.value === 'go') {
                        code = generateGoCode(selector, fields);
                    } else if (codeLanguage.value === 'python') {
                        code = generatePythonCode(selector, fields);
                    } else if (codeLanguage.value === 'javascript') {
                        code = generateJavaScriptCode(selector, fields);
                    }

                    monacoEditor.value.setValue(code);
                };

                const generateGoCode = (selector, fields) => {
                    const structFields = fields.map(f => {
                        const fieldName = f.name.charAt(0).toUpperCase() + f.name.slice(1);
                        return `\t${fieldName} string // ${f.name}`;
                    }).join('\n');

                    const extractionCode = fields.map(f => {
                        if (f.type === 'attribute') {
                            return `\t\t// 提取${f.name}\n\t\t${f.name} := e.DOM.Find("${f.selector}").AttrOr("${f.attribute}", "")`;
                        } else {
                            return `\t\t// 提取${f.name}\n\t\t${f.name} := e.DOM.Find("${f.selector}").Text()`;
                        }
                    }).join('\n\n');

                    const assignmentCode = fields.map(f => {
                        const fieldName = f.name.charAt(0).toUpperCase() + f.name.slice(1);
                        return `\t\t\t${fieldName}: ${f.name},`;
                    }).join('\n');

                    return `package main

import (
\t"fmt"
\t"log"

\t"github.com/gocolly/colly"
)

type DataItem struct {
${structFields}
}

func main() {
\tc := colly.NewCollector(
\t\tcolly.AllowedDomains("example.com"),
\t\tcolly.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
\t)

\tc.OnHTML("${selector}", func(e *colly.HTMLElement) {
${extractionCode}

\t\titem := &DataItem{
${assignmentCode}
\t\t}

\t\tfmt.Printf("Found Item: %+v\\n", item)
\t})

\tc.OnError(func(r *colly.Response, err error) {
\t\tfmt.Println("请求错误:", err)
\t})

\tlog.Println("正在访问目标网站...")
\terr := c.Visit("https://example.com")
\tif err != nil {
\t\tlog.Fatalf("Error visiting URL: %v", err)
\t}

\tlog.Println("爬取完成")
}`;
                };

                const generatePythonCode = (selector, fields) => {
                    const extractionCode = fields.map(f => {
                        if (f.type === 'attribute') {
                            return `        ${f.name} = item.css('${f.selector}::attr(${f.attribute})').get() or ''`;
                        } else {
                            return `        ${f.name} = item.css('${f.selector}::text').get() or ''`;
                        }
                    }).join('\n');

                    return `import scrapy

class DataSpider(scrapy.Spider):
    name = 'data_spider'
    allowed_domains = ['example.com']
    start_urls = ['https://example.com']

    def parse(self, response):
        items = response.css('${selector}')

        for item in items:
${extractionCode}

            yield {
${fields.map(f => `                '${f.name}': ${f.name},`).join('\n')}
            }`;
                };

                const generateJavaScriptCode = (selector, fields) => {
                    const extractionCode = fields.map(f => {
                        if (f.type === 'attribute') {
                            return `            const ${f.name} = element.querySelector('${f.selector}')?.getAttribute('${f.attribute}') || '';`;
                        } else {
                            return `            const ${f.name} = element.querySelector('${f.selector}')?.textContent?.trim() || '';`;
                        }
                    }).join('\n');

                    return `const puppeteer = require('puppeteer');

(async () => {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    await page.goto('https://example.com');

    const results = await page.evaluate(() => {
        const items = document.querySelectorAll('${selector}');
        const data = [];

        items.forEach(element => {
${extractionCode}

            data.push({
${fields.map(f => `                ${f.name},`).join('\n')}
            });
        });

        return data;
    });

    console.log('提取结果:', results);
    await browser.close();
})();`;
                };

                const copyCode = async () => {
                    if (monacoEditor.value) {
                        const code = monacoEditor.value.getValue();
                        try {
                            await navigator.clipboard.writeText(code);
                            ElMessage.success('代码已复制到剪贴板');
                        } catch (err) {
                            ElMessage.error('复制失败');
                        }
                    }
                };

                const exportCode = () => {
                    if (monacoEditor.value) {
                        const code = monacoEditor.value.getValue();
                        const extensions = { go: '.go', python: '.py', javascript: '.js' };
                        const filename = `${currentProject.value.name.replace(/[^\w\s]/gi, '')}_scraper${extensions[codeLanguage.value]}`;

                        const blob = new Blob([code], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        a.click();
                        URL.revokeObjectURL(url);

                        ElMessage.success('代码文件已导出');
                    }
                };

                const exportResults = () => {
                    if (results.value.length === 0) {
                        ElMessage.warning('暂无数据可导出');
                        return;
                    }

                    const csv = [
                        currentProject.value.fields.map(f => f.name).join(','),
                        ...results.value.map(row =>
                            currentProject.value.fields.map(f => `"${(row[f.name] || '').replace(/"/g, '""')}"`).join(',')
                        )
                    ].join('\n');

                    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${currentProject.value.name}_results.csv`;
                    a.click();
                    URL.revokeObjectURL(url);

                    ElMessage.success('数据已导出为CSV文件');
                };

                const saveProject = () => {
                    const data = JSON.stringify(projects.value, null, 2);
                    const blob = new Blob([data], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'scraper_projects.json';
                    a.click();
                    URL.revokeObjectURL(url);

                    // 标记为已保存
                    projects.value.forEach(p => p.hasUnsavedChanges = false);
                    ElMessage.success('项目已保存');
                };

                const loadProject = () => {
                    document.querySelector('input[type="file"]').click();
                };

                const handleFileLoad = (event) => {
                    const file = event.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            try {
                                const data = JSON.parse(e.target.result);
                                projects.value = data;
                                currentProjectIndex.value = '0';
                                ElMessage.success('项目加载成功');
                            } catch (error) {
                                ElMessage.error('文件格式错误');
                            }
                        };
                        reader.readAsText(file);
                    }
                };

                const showReference = () => {
                    showReferenceDialog.value = true;
                };

                const closeReference = () => {
                    showReferenceDialog.value = false;
                };

                const trySelector = (selector) => {
                    currentProject.value.selector = selector;
                    testSelector();
                    closeReference();
                    ElMessage.success(`已应用选择器: ${selector}`);
                };

                const suggestSelectors = () => {
                    ElMessage.info('智能建议功能开发中...');
                };

                const undoAction = () => {
                    ElMessage.info('撤销功能开发中...');
                };

                // 监听语言变化
                watch(codeLanguage, () => {
                    generateCode();
                });

                // 组件挂载后初始化Monaco Editor
                onMounted(async () => {
                    await nextTick();

                    // 初始化Monaco Editor
                    require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.34.1/min/vs' } });
                    require(['vs/editor/editor.main'], () => {
                        const container = document.querySelector('.monaco-container');
                        if (container) {
                            monacoEditor.value = monaco.editor.create(container, {
                                value: '// 请配置选择器和字段后生成代码',
                                language: 'go',
                                theme: 'vs-dark',
                                fontSize: 14,
                                minimap: { enabled: false },
                                scrollBeyondLastLine: false,
                                automaticLayout: true
                            });

                            // 初始生成代码
                            generateCode();
                        }
                    });
                });

                return {
                    // 响应式数据
                    isDarkMode,
                    currentProjectIndex,
                    codeLanguage,
                    showReferenceDialog,
                    canUndo,
                    results,
                    projects,
                    currentProject,

                    // 方法
                    toggleTheme,
                    createNewProject,
                    switchProject,
                    closeProject,
                    addField,
                    addPresetField,
                    removeField,
                    onHtmlChange,
                    onSelectorChange,
                    onFieldChange,
                    testSelector,
                    copyCode,
                    exportCode,
                    exportResults,
                    saveProject,
                    loadProject,
                    handleFileLoad,
                    showReference,
                    closeReference,
                    trySelector,
                    suggestSelectors,
                    undoAction,

                    // Element Plus 图标
                    ...icons
                };
            }
        });

        // 注册所有图标组件
        Object.keys(icons).forEach(key => {
            app.component(key, icons[key]);
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
