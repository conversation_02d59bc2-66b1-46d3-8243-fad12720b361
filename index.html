<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级爬虫选择器调试工具 (专业布局版)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/go.min.js"></script>
    <link rel="stylesheet" href="style.css">
    <!-- 在 <head> 里加入 Monaco Editor loader.js CDN -->
    <script src="https://unpkg.com/monaco-editor@0.44.0/min/vs/loader.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="navbar-left">
            <span class="logo">️ Colly CSS 选择器调试工具</span>
        </div>
        <div class="navbar-right">
            <button id="theme-toggle-btn" class="nav-btn" title="深色/浅色模式切换">🌙</button>
            <button id="save-project-btn" class="nav-btn" title="保存当前工程 (Ctrl+S)">💾</button>
            <button id="load-project-btn" class="nav-btn" title="导入工程">📂</button>
            <input type="file" id="load-project-file" accept="application/json" style="display:none;" />
            <button id="undo-btn" class="nav-btn" title="撤销上一步 (Ctrl+Z)" disabled>↩️</button>
        </div>
    </nav>

    <div class="main-layout-3col">
        <div class="main-col html-col">
            <div class="input-section">
                <label for="html-input">HTML 代码:</label>
                <textarea id="html-input" placeholder="请输入或粘贴 HTML 代码..."></textarea>
                <button id="format-html-btn" class="main-btn" type="button" title="一键格式化HTML">
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none" style="vertical-align:middle;margin-right:6px;"><path d="M3 17l7-7" stroke="#fff" stroke-width="2" stroke-linecap="round"/><path d="M13.5 6.5l2-2M16 4l-2.5 2.5" stroke="#fff" stroke-width="2" stroke-linecap="round"/><rect x="2" y="16" width="4" height="2" rx="1" fill="#fff"/></svg>
                    格式化HTML
                </button>
            </div>
        </div>
        <div class="main-col config-col">
            <div class="input-section">
                <div class="form-group">
                    <div class="label-with-button">
                        <label for="list-item-selector">列表项选择器 (父元素):</label>
                        <button id="open-reference-btn" class="reference-btn">CSS 选择器参考手册</button>
                    </div>
                    <input type="text" id="list-item-selector" class="selector-input" placeholder="例如: .product-card">
                </div>
                <hr style="margin: 20px 0;">
                <h4>提取字段 (可拖拽排序):</h4>
                <div id="fields-container"></div>
                <div id="add-field-action-area">
                    <button id="add-field-btn" class="add-field-btn-main" type="button" title="添加新字段 (Ctrl+Enter)">+ 添加新字段</button>
                    <div class="field-templates" style="margin-top: 10px;">
                        <label style="font-size: 12px; color: #666;">快速添加:</label>
                        <button class="template-btn" onclick="addField('title', 'h1, h2, h3', 'text')" title="标题字段">📝 标题</button>
                        <button class="template-btn" onclick="addField('link', 'a', 'attribute', 'href')" title="链接字段">🔗 链接</button>
                        <button class="template-btn" onclick="addField('image', 'img', 'attribute', 'src')" title="图片字段">🖼️ 图片</button>
                        <button class="template-btn" onclick="addField('price', '.price, [class*=price]', 'text')" title="价格字段">💰 价格</button>
                    </div>
                </div>
                <button class="btn-test" onclick="testSelector()" style="margin-top:20px;width:100%;" title="手动测试与提取 (F5)">🔍 手动测试与提取</button>
            </div>
        </div>
        <div class="main-col result-col">
            <div class="results-section">
                <h2>🎯 匹配结果</h2>
                <div id="results-container" class="results-container">
                    <div class="no-matches">请选择模式，输入代码和选择器，结果将实时更新</div>
                </div>
            </div>
        </div>
    </div>
    <div class="code-section">
      <div class="code-card">
        <div class="framework-tabs" id="project-tabs">
          <button class="framework-tab active">默认工程</button>
          <button class="add-project-tab">+</button>
        </div>
        <div class="code-block">
          <div id="editor" style="width:100%;height:600px;"></div>
          <div class="editor-toolbar">
            <label for="language-select" style="color:#b3e5fc;font-size:15px;margin-right:8px;">类型：</label>
            <select id="language-select" style="font-size:15px;padding:2px 8px;border-radius:6px;">
              <option value="go">Go</option>
              <option value="javascript">JavaScript</option>
              <option value="python">Python</option>
              <option value="json">JSON</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="markdown">Markdown</option>
            </select>
            <button class="copy-btn" onclick="copyCode()" style="margin-left:auto;">📋 复制代码</button>
            <button class="export-btn" onclick="exportCode()" style="margin-left:8px;">💾 导出文件</button>
          </div>
        </div>
      </div>
    </div>

    <!-- CSS 选择器参考手册模态窗口 -->
    <div id="reference-modal" class="modal-overlay">
        <div class="modal-content">
            <button id="close-modal-btn" class="close-modal-btn">&times;</button>
            <h2>CSS 选择器参考手册</h2>
            <div class="accordion">
                <div class="accordion-item">
                    <button class="accordion-header">元素选择器 (Element Selector)</button>
                    <div class="accordion-content-inner">
                        <p>根据HTML标签名选择元素。</p>
                        <div class="example-box">
                            <span>示例: <code>h2</code> (选择所有h2标题)</span>
                            <button class="try-it-btn" onclick="trySelector('h2')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">ID 选择器 (ID Selector)</button>
                    <div class="accordion-content-inner">
                        <p>根据元素的唯一ID选择，ID前加 <code>#</code>。请注意，ID在页面中应当是唯一的。</p>
                        <div class="example-box">
                            <span>示例: <code>#main-title</code> (本示例中无此ID，可自行添加)</span>
                            <button class="try-it-btn" onclick="trySelector('#main-title')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">类选择器 (Class Selector)</button>
                    <div class="accordion-content-inner">
                        <p>根据元素的class属性选择，类名前加 <code>.</code>。</p>
                        <div class="example-box">
                            <span>示例: <code>.product-card</code> (选择所有class包含product-card的元素)</span>
                            <button class="try-it-btn" onclick="trySelector('.product-card')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">属性选择器 (Attribute Selector)</button>
                    <div class="accordion-content-inner">
                        <p>根据元素的属性或属性值选择，用 <code>[]</code> 包裹。</p>
                        <div class="example-box">
                            <span>示例: <code>[data-id]</code> (选择所有带有data-id属性的元素)</span>
                            <button class="try-it-btn" onclick="trySelector('[data-id]')">试用</button>
                        </div>
                        <div class="example-box">
                            <span>示例: <code>[data-id="p001"]</code> (选择data-id属性值等于p001的元素)</span>
                            <button class="try-it-btn" onclick="trySelector('[data-id=\'p001\']')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">后代选择器 (Descendant Selector)</button>
                    <div class="accordion-content-inner">
                        <p>用空格隔开，选择某个元素内部的所有后代元素（儿子、孙子等）。</p>
                        <div class="example-box">
                            <span>示例: <code>.product-card .price</code> (选择.product-card内的所有.price元素)</span>
                            <button class="try-it-btn" onclick="trySelector('.product-card .price')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">子代选择器 (Child Selector)</button>
                    <div class="accordion-content-inner">
                        <p>用 <code>&gt;</code> 隔开，只选择某个元素的直接子元素。</p>
                        <div class="example-box">
                            <span>示例: <code>.price-box > .price</code> (只选择.price-box的直接子元素.price)</span>
                            <button class="try-it-btn" onclick="trySelector('.price-box > .price')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">伪类选择器 (Pseudo-class Selector)</button>
                    <div class="accordion-content-inner">
                        <p>用于选择处于特定状态的元素，如列表中的第N个元素。对爬虫非常有用。</p>
                        <div class="example-box">
                            <span>示例: <code>.product-card:nth-child(2)</code> (选择第二个.product-card)</span>
                            <button class="try-it-btn" onclick="trySelector('.product-card:nth-child(2)')">试用</button>
                        </div>
                        <div class="example-box">
                            <span>示例: <code>.product-card:first-child</code> (选择第一个.product-card)</span>
                            <button class="try-it-btn" onclick="trySelector('.product-card:first-child')">试用</button>
                        </div>
                         <div class="example-box">
                            <span>示例: <code>.product-card:not(:first-child)</code> (选择非第一个.product-card)</span>
                            <button class="try-it-btn" onclick="trySelector('.product-card:not(:first-child)')">试用</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">兄弟选择器 (Sibling Combinators)</button>
                    <div class="accordion-content-inner">
                        <p>用于选择与另一个元素具有相同父级的元素。</p>
                        <div class="example-box">
                            <span>相邻兄弟选择器 (<code>+</code>): 选择紧接在指定元素后的第一个兄弟元素。</span>
                            <button class="try-it-btn" onclick="trySelector('h2 + p')">试用 h2 + p</button>
                        </div>
                        <div class="example-box">
                            <span>通用兄弟选择器 (<code>~</code>): 选择在指定元素之后的所有兄弟元素。</span>
                            <button class="try-it-btn" onclick="trySelector('h2 ~ p')">试用 h2 ~ p</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">更多属性选择器 (More Attribute Selectors)</button>
                    <div class="accordion-content-inner">
                        <p>除了精确匹配，还可以根据属性值的开头、结尾或包含内容进行选择。</p>
                        <div class="example-box">
                            <span>开头匹配 (<code>[attr^=value]</code>): 属性值以指定字符串开头。</span>
                            <button class="try-it-btn" onclick="trySelector('a[href^="/product/"]')">试用 a[href^="/product/"]</button>
                        </div>
                        <div class="example-box">
                            <span>结尾匹配 (<code>[attr$=value]</code>): 属性值以指定字符串结尾。</span>
                            <button class="try-it-btn" onclick="trySelector('img[src$=".jpg"]')">试用 img[src$=".jpg"]</button>
                        </div>
                        <div class="example-box">
                            <span>包含匹配 (<code>[attr*=value]</code>): 属性值包含指定字符串。</span>
                            <button class="try-it-btn" onclick="trySelector('h2[class*="product"]')">试用 h2[class*="product"]</button>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-header">结构性伪类 (Structural Pseudo-classes)</button>
                    <div class="accordion-content-inner">
                        <p>根据元素在文档树中的位置进行选择，与元素类型无关。</p>
                        <div class="example-box">
                            <span><code>:nth-of-type(n)</code>: 选择同类型元素的第 N 个。</span>
                            <button class="try-it-btn" onclick="trySelector('span.price:nth-of-type(1)')">试用 span.price:nth-of-type(1)</button>
                        </div>
                        <div class="example-box">
                            <span><code>:first-of-type</code>: 选择同类型元素的第一个。</span>
                            <button class="try-it-btn" onclick="trySelector('span.price:first-of-type')">试用 span.price:first-of-type</button>
                        </div>
                        <div class="example-box">
                            <span><code>:last-of-type</code>: 选择同类型元素的最后一个。</span>
                            <button class="try-it-btn" onclick="trySelector('span.price:last-of-type')">试用 span.price:last-of-type</button>
                        </div>
                        <div class="example-box">
                            <span><code>:only-child</code>: 选择是其父元素的唯一子元素。</span>
                            <button class="try-it-btn" onclick="trySelector('.price-box span:only-child')">试用 .price-box span:only-child</button>
                        </div>
                        <div class="example-box">
                            <span><code>:only-of-type</code>: 选择是其父元素的唯一同类型子元素。</span>
                            <button class="try-it-btn" onclick="trySelector('div.rating:only-of-type')">试用 div.rating:only-of-type</button>
                        </div>
                        <div class="example-box">
                            <span><code>:empty</code>: 选择不包含任何子元素（包括文本节点）的元素。</span>
                            <button class="try-it-btn" onclick="trySelector('div:empty')">试用 div:empty</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 字段卡片模板 -->
    <template id="field-card-template">
        <div class="field-card" draggable="true">
            <div class="drag-handle">⠿</div>
            <div class="field-inputs">
                <div class="field-group">
                    <label>字段名</label>
                    <input type="text" class="field-name" placeholder="e.g., title">
                </div>
                <div class="field-group">
                    <label>子选择器</label>
                    <input type="text" class="sub-selector" placeholder="e.g., h2.name">
                </div>
                <div class="field-group">
                    <label>提取类型</label>
                    <select class="extract-type">
                        <option value="text">文本 (Text)</option>
                        <option value="attribute">属性 (Attribute)</option>
                    </select>
                </div>
                <div class="field-group attr-name-group" style="display: none;">
                    <label>属性名</label>
                    <input type="text" class="attr-name" placeholder="e.g., href, src">
                </div>
            </div>
            <button class="remove-field-btn" title="删除" type="button">×</button>
        </div>
    </template>

    <!-- 工程右键菜单 -->
    <ul id="project-context-menu" class="project-context-menu">
      <li id="rename-project">重命名</li>
      <li id="delete-project">删除</li>
    </ul>

    <script src="https://cdn.jsdelivr.net/npm/js-beautify@1.14.13/js/lib/beautify-html.min.js"></script>
    <script src="script.js"></script>
</body>
</html>