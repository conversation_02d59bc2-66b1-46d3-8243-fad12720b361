<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试添加字段功能</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .field-card { 
            background: #ffffff; 
            border: 1px solid #e0e0e0; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.05); 
            display: flex; 
            align-items: center; 
            padding: 16px; 
            gap: 12px; 
            margin: 10px 0;
        }
        .field-inputs { flex-grow: 1; display: flex; flex-wrap: wrap; gap: 12px; }
        .field-group { display: flex; flex-direction: column; flex: 1 1 120px; min-width: 120px; }
        .field-group label { font-size: 13px; font-weight: 500; margin-bottom: 4px; color: #555; }
        .field-group input, .field-group select { padding: 8px 10px; font-size: 14px; border-radius: 6px; border: 1px solid #ccc; }
        .remove-field-btn { 
            background: #f44336; 
            color: white; 
            border: none; 
            border-radius: 50%; 
            width: 28px; 
            height: 28px; 
            font-weight: bold; 
            cursor: pointer; 
            font-size: 16px; 
        }
        .add-field-btn-main { 
            display: block; 
            width: 100%; 
            padding: 12px; 
            margin: 16px 0; 
            background: #1e88e5; 
            color: white; 
            border: none; 
            border-radius: 8px; 
            font-weight: bold; 
            cursor: pointer; 
        }
        #fields-container { 
            display: flex; 
            flex-direction: column; 
            gap: 16px; 
            min-height: 50px;
            border: 2px dashed #ddd;
            padding: 10px;
            border-radius: 8px;
        }
        .debug-info { 
            background: #f0f0f0; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            font-family: monospace; 
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>调试添加字段功能</h1>
        
        <div class="debug-info" id="debug-info">
            等待初始化...
        </div>
        
        <button id="add-field-btn" class="add-field-btn-main">+ 添加新字段</button>
        <button onclick="clearFields()">清空字段</button>
        <button onclick="showDebugInfo()">显示调试信息</button>
        
        <h3>字段容器:</h3>
        <div id="fields-container">
            <p style="color: #999; text-align: center; margin: 20px 0;">暂无字段</p>
        </div>

        <!-- 字段卡片模板 -->
        <template id="field-card-template">
            <div class="field-card" draggable="true">
                <div class="field-inputs">
                    <div class="field-group">
                        <label>字段名</label>
                        <input type="text" class="field-name" placeholder="e.g., title">
                    </div>
                    <div class="field-group">
                        <label>子选择器</label>
                        <input type="text" class="sub-selector" placeholder="e.g., h2.name">
                    </div>
                    <div class="field-group">
                        <label>提取类型</label>
                        <select class="extract-type">
                            <option value="text">文本 (Text)</option>
                            <option value="attribute">属性 (Attribute)</option>
                        </select>
                    </div>
                    <div class="field-group attr-name-group" style="display: none;">
                        <label>属性名</label>
                        <input type="text" class="attr-name" placeholder="e.g., href, src">
                    </div>
                </div>
                <button class="remove-field-btn" title="删除" type="button">×</button>
            </div>
        </template>
    </div>

    <script>
        let fieldCount = 0;
        
        function log(message) {
            const debugInfo = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            console.log(message);
        }
        
        function addField(name = '', selector = '', type = 'text', attr = '') {
            log(`开始添加字段: name=${name}, selector=${selector}, type=${type}, attr=${attr}`);
            
            const template = document.getElementById('field-card-template');
            if (!template) {
                log('错误: 找不到字段模板元素');
                return;
            }
            log('找到字段模板');
            
            const clone = template.content.cloneNode(true);
            const fieldCard = clone.querySelector('.field-card');
            
            if (!fieldCard) {
                log('错误: 找不到字段卡片元素');
                return;
            }
            log('成功克隆字段卡片');
            
            // 安全地设置字段值
            const fieldNameInput = fieldCard.querySelector('.field-name');
            const subSelectorInput = fieldCard.querySelector('.sub-selector');
            const extractTypeSelect = fieldCard.querySelector('.extract-type');
            
            if (fieldNameInput) {
                fieldNameInput.value = name;
                log('设置字段名: ' + name);
            }
            if (subSelectorInput) {
                subSelectorInput.value = selector;
                log('设置子选择器: ' + selector);
            }
            if (extractTypeSelect) {
                extractTypeSelect.value = type;
                log('设置提取类型: ' + type);
            }
            
            const attrGroup = fieldCard.querySelector('.attr-name-group');
            const attrInput = fieldCard.querySelector('.attr-name');
            if (attrGroup && attrInput) {
                if (type === 'attribute') {
                    attrGroup.style.display = 'flex';
                    attrInput.value = attr;
                    log('显示属性组，设置属性名: ' + attr);
                } else {
                    attrGroup.style.display = 'none';
                    log('隐藏属性组');
                }
            }

            // 绑定删除按钮事件
            const removeBtn = fieldCard.querySelector('.remove-field-btn');
            if (removeBtn) {
                removeBtn.onclick = () => {
                    log('删除字段');
                    fieldCard.remove();
                    fieldCount--;
                    updateFieldCount();
                };
            }

            // 绑定类型选择器事件
            if (extractTypeSelect && attrGroup) {
                extractTypeSelect.onchange = (e) => {
                    const isAttribute = e.target.value === 'attribute';
                    attrGroup.style.display = isAttribute ? 'flex' : 'none';
                    log('切换提取类型: ' + e.target.value);
                };
            }

            // 添加到容器
            const fieldsContainer = document.getElementById('fields-container');
            if (!fieldsContainer) {
                log('错误: 找不到字段容器元素');
                return;
            }
            
            // 清空提示文本
            const placeholder = fieldsContainer.querySelector('p');
            if (placeholder) {
                placeholder.remove();
                log('移除占位符文本');
            }
            
            fieldsContainer.appendChild(clone);
            fieldCount++;
            updateFieldCount();
            log('字段已成功添加到容器');
        }
        
        function clearFields() {
            const fieldsContainer = document.getElementById('fields-container');
            fieldsContainer.innerHTML = '<p style="color: #999; text-align: center; margin: 20px 0;">暂无字段</p>';
            fieldCount = 0;
            updateFieldCount();
            log('清空所有字段');
        }
        
        function updateFieldCount() {
            log(`当前字段数量: ${fieldCount}`);
        }
        
        function showDebugInfo() {
            const template = document.getElementById('field-card-template');
            const container = document.getElementById('fields-container');
            const button = document.getElementById('add-field-btn');
            
            log('=== 调试信息 ===');
            log('模板元素存在: ' + (template ? '是' : '否'));
            log('容器元素存在: ' + (container ? '是' : '否'));
            log('按钮元素存在: ' + (button ? '是' : '否'));
            log('当前字段数量: ' + fieldCount);
            log('容器子元素数量: ' + (container ? container.children.length : 0));
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('DOM 加载完成');
            
            const addBtn = document.getElementById('add-field-btn');
            if (addBtn) {
                addBtn.addEventListener('click', () => {
                    log('添加字段按钮被点击');
                    addField(`字段${fieldCount + 1}`, `.selector${fieldCount + 1}`, 'text');
                });
                log('添加字段按钮事件已绑定');
            } else {
                log('错误: 找不到添加字段按钮');
            }
            
            // 添加一些测试字段
            log('添加测试字段...');
            addField('title', 'h2.product-title', 'text');
            addField('price', '.price', 'text');
            addField('url', 'a', 'attribute', 'href');
            
            showDebugInfo();
        });
    </script>
</body>
</html>
