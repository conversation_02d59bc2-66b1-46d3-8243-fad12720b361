:root {
    --main-bg: #f7f9fc;
    --main-text: #333;
    --header-bg: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    --header-text: #fff;
    --input-bg: #fff;
    --input-border: #e0e0e0;
    --field-bg: #fff;
    --field-border: #e0e0e0;
    --btn-bg: #1e88e5;
    --btn-hover: #1565c0;
    --code-bg: #2c3e50;
    --code-block-bg: #34495e;
    --code-border: #4a5f7a;
    --modal-bg: #fff;
    --modal-overlay: rgba(0,0,0,0.6);
}
body.dark-mode {
    --main-bg: #1a1a1a;
    --main-text: #e0e0e0;
    --header-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --header-text: #ffffff;
    --input-bg: #252525;
    --input-border: #404040;
    --field-bg: #303030;
    --field-border: #505050;
    --btn-bg: #3498db;
    --btn-hover: #5dade2;
    --code-bg: #202020;
    --code-block-bg: #282828;
    --code-border: #404040;
    --modal-bg: #252525;
    --modal-overlay: rgba(0,0,0,0.7);
}

.dark-mode .field-card {
    background: #303030;
    border-color: #505050;
}

.dark-mode .field-group label {
    color: #b0b0b0;
}

.dark-mode .field-group input,
.dark-mode .field-group select {
    background: #252525;
    border-color: #505050;
    color: #e0e0e0;
}

.dark-mode .results-table th {
    background-color: #303030;
}

.dark-mode .results-table tr:nth-child(even) {
    background-color: #282828;
}

.dark-mode .results-table td, .dark-mode .results-table th {
    border-color: #404040;
}

.dark-mode .main-col {
    background: var(--input-bg);
}

.dark-mode .form-group input, .dark-mode .form-group textarea, .dark-mode .form-group select {
    background: var(--field-bg);
    color: var(--main-text);
    border-color: var(--input-border);
}

.dark-mode .selector-input {
    background: #444;
    border-color: #ffc107;
}

.dark-mode .code-section {
    background: var(--code-bg);
    color: var(--header-text);
}

.dark-mode .framework-tab {
    background: var(--code-block-bg);
    color: white;
}

.dark-mode .framework-tab.active {
    background: var(--btn-bg);
}

.dark-mode .code-block {
    background: var(--code-block-bg);
    border-color: var(--code-border);
}

.dark-mode .results-container {
    background: var(--field-bg);
    border-color: var(--input-border);
}

.dark-mode .no-matches {
    color: #999;
}

.dark-mode .main-btn {
    background: var(--btn-bg);
}

.dark-mode .main-btn:hover {
    background: var(--btn-hover);
}

.dark-mode .btn-test {
    /* No changes needed, already has a gradient */
}

.dark-mode .remove-field-btn {
    /* No changes needed, already has a distinct color */
}

.dark-mode .add-field-btn-main {
    background: var(--btn-bg);
}

.dark-mode .add-field-btn-main:hover {
    background: var(--btn-hover);
}

.add-project-tab {
    background: #388bfd;
    color: #fff;
    border: none;
    border-radius: 10px;
    padding: 14px 38px;
    font-size: 22px;
    font-family: inherit;
    cursor: pointer;
    margin-left: 0;
    transition: background 0.2s;
}
.add-project-tab:hover {
    background: #2563eb;
}


* { margin: 0; padding: 0; box-sizing: border-box; }
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: var(--main-bg); color: var(--main-text); }

.navbar { width: 100%; height: 60px; background: var(--header-bg); color: var(--header-text); display: flex; align-items: center; justify-content: space-between; padding: 0 40px; box-sizing: border-box; position: sticky; top: 0; z-index: 3000; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.navbar-left .logo { font-size: 1.3em; font-weight: bold; letter-spacing: 1px; }
.navbar-right { display: flex; align-items: center; gap: 12px; }
.nav-btn { background: none; border: none; color: var(--header-text); font-size: 1.3em; cursor: pointer; padding: 6px 12px; border-radius: 6px; transition: background 0.2s; }
.nav-btn:disabled { opacity: 0.5; cursor: not-allowed; }
.nav-btn:hover:not(:disabled) { background: rgba(255,255,255,0.12); }

.main-layout-3col { display: flex; flex-direction: row; gap: 24px; max-width: 95vw; margin: 24px auto; padding: 0 24px; }
.main-col { background: var(--input-bg); border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); padding: 24px; display: flex; flex-direction: column; }

.html-col { flex: 1; min-width: 300px; }
.config-col { flex: 1.5; min-width: 450px; }
.result-col { flex: 1; min-width: 300px; }

.input-section { background: transparent; padding: 0; border: none; border-radius: 0; }
.code-section h2, .results-section h2 { color: var(--main-text); margin-bottom: 20px; font-size: 1.5em; }

.dark-mode .code-section h2,
.dark-mode .results-section h2 {
    color: #ffffff !important; /* Explicitly set to white in dark mode */
}

.form-group { margin-bottom: 20px; }
.form-group label { display: block; margin-bottom: 8px; font-weight: bold; color: #555; }
.form-group input, .form-group textarea, .form-group select { width: 100%; padding: 12px 15px; border: 1px solid var(--input-border); border-radius: 8px; font-size: 14px; transition: all 0.3s ease; font-family: 'Consolas', 'Monaco', monospace; background: var(--field-bg); color: var(--main-text); }
.form-group input:focus, .form-group textarea:focus, .form-group select:focus { outline: none; border-color: var(--btn-bg); box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.1); }
.html-col .input-section {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.html-col .form-group {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

#html-input {
    flex-grow: 1;
    resize: vertical;
    min-height: 60vh;
}

.selector-input { background: #fff3cd; border-color: #ffc107; }

.btn-test { background: linear-gradient(135deg, #FF6B6B 0%, #ee5a52 100%); color: white; padding: 12px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; transition: all 0.3s ease; width: 100%; margin-top: 10px; }
.btn-test:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3); }

.results-section { background: transparent; padding: 0; border: none; border-radius: 0; }
.results-container { background: var(--field-bg); border-radius: 8px; padding: 20px; border: 1px solid var(--input-border); max-height: 450px; overflow: auto; }
.no-matches { text-align: center; color: #666; padding: 40px; font-size: 16px; }
.no-matches::before { content: "😕"; display: block; font-size: 3em; margin-bottom: 15px; }

.code-section {
    margin: 48px 0 0 0;
    width: 100vw;
    background: #f5f7fa;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    padding: 0 0.05vw;
}

.code-card {
    margin: 0;
    width: 100%;
    max-width: 2400px;
    background: #23272e;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.13);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.code-toolbar {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 2vw;
    gap: 18px;
    border-bottom: 1.5px solid #282c34;
    min-height: 64px;
}

.framework-tabs {
    display: flex;
    gap: 8px;
    padding: 0 36px;
    min-height: 48px;
    background: #262b33;
    border-bottom: 1.5px solid #23272e;
}

.framework-tab, .add-project-tab {
    background: #23272e;
    color: #b3e5fc;
    border: none;
    border-radius: 8px 8px 0 0;
    padding: 8px 22px;
    font-size: 15px;
    font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'Menlo', monospace;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    font-weight: 500;
    margin-bottom: 0;
    outline: none;
    box-shadow: none;
}
.framework-tab.active, .framework-tab:hover {
    background: #388bfd;
    color: #fff;
    font-weight: 700;
    box-shadow: 0 2px 12px #388bfd44;
}
.add-project-tab {
    background: #388bfd;
    color: #fff;
}
.add-project-tab:hover {
    background: #2563eb;
}

.code-block {
    background: #282c34;
    border-radius: 0 0 18px 18px;
    margin: 0;
    padding: 0;
    min-height: 600px;
    max-height: none;
    box-shadow: none;
    border: none;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.copy-btn, .export-btn {
    background: #388bfd;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 8px 18px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s;
    box-shadow: 0 2px 8px rgba(56,139,253,0.10);
}
.copy-btn:hover, .export-btn:hover {
    background: #2563eb;
}
.export-btn {
    background: #28a745;
}
.export-btn:hover {
    background: #218838;
}

.code-block pre {
    margin: 0;
    background: none;
    border: none;
    padding: 0;
    overflow-x: auto;
}

.code-block code {
    background: none;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    white-space: pre;
    display: block;
}

.dark-mode .code-block pre {
    color: #ffffff;
}
.syntax-highlight { color: #e74c3c; } .string-highlight { color: #2ecc71; } .keyword-highlight { color: #3498db; } .comment-highlight { color: #95a5a6; }

/* 卡片式字段设计 */
#fields-container { display: flex; flex-direction: column; gap: 16px; }
.field-card { background: #ffffff; border: 1px solid #e0e0e0; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); display: flex; align-items: center; padding: 16px; gap: 12px; transition: box-shadow 0.2s, transform 0.2s; }
.field-card.dragging { opacity: 0.7; box-shadow: 0 8px 32px rgba(30,136,229,0.18); transform: scale(1.02); cursor: grabbing; z-index: 10; }
.field-card.drag-over { border-color: var(--btn-bg); }

.drag-handle { font-size: 24px; color: #9e9e9e; cursor: grab; user-select: none; padding-top: 16px; }
.field-inputs { flex-grow: 1; display: flex; flex-wrap: wrap; gap: 12px; }
.field-group { display: flex; flex-direction: column; flex: 1 1 120px; min-width: 120px; }
.field-group label { font-size: 13px; font-weight: 500; margin-bottom: 4px; color: #555; }
.field-group input, .field-group select { padding: 8px 10px; font-size: 14px; border-radius: 6px; border: 1px solid #ccc; }

.remove-field-btn { background: #f44336; color: white; border: none; border-radius: 50%; width: 28px; height: 28px; font-weight: bold; cursor: pointer; transition: background 0.2s; font-size: 16px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; }
.remove-field-btn:hover { background: #c62828; }

.add-field-btn-main { display: block; width: 100%; padding: 12px; margin-top: 16px; background: var(--btn-bg); color: white; border: none; border-radius: 8px; font-weight: bold; cursor: pointer; transition: background 0.2s; }
.add-field-btn-main:hover { background: var(--btn-hover); }

.field-templates { display: flex; flex-wrap: wrap; gap: 6px; align-items: center; }
.template-btn {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}
.template-btn:hover {
    background: var(--btn-bg);
    color: white;
    border-color: var(--btn-bg);
}
.dark-mode .template-btn {
    background: #404040;
    border-color: #555;
    color: #ccc;
}
.dark-mode .template-btn:hover {
    background: var(--btn-bg);
    color: white;
}

.main-btn { background: var(--btn-bg); color: #fff; border: none; border-radius: 6px; padding: 10px 0; font-size: 15px; font-weight: 500; width: 100%; margin-top: 10px; cursor: pointer; transition: background 0.2s; box-shadow: 0 2px 8px rgba(30, 136, 229, 0.08); display: flex; align-items: center; justify-content: center; gap: 6px; }
.main-btn:hover { background: var(--btn-hover); }

.results-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
}
.dark-mode .results-stats {
    background: #2d3748;
    color: #e2e8f0;
}
.stats-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.results-table { width: 100%; border-collapse: collapse; font-size: 14px; margin-top: 10px; }
.results-table th, .results-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
.results-table th { background-color: #f2f2f2; font-weight: bold; }
.results-table tr:nth-child(even) { background-color: #f9f9f9; }
.results-table td { word-break: break-all; }

.label-with-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.reference-btn {
    background: none;
    border: 1px solid var(--btn-bg);
    color: var(--btn-bg);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}

.reference-btn:hover {
    background: var(--btn-bg);
    color: white;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--modal-overlay);
    z-index: 1000;
    display: none; /* Hidden by default */
    align-items: center;
    justify-content: center;
}

.modal-overlay.active {
    display: flex; /* Show when active */
}

.modal-content {
    background: var(--modal-bg);
    padding: 30px;
    border-radius: 10px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.close-modal-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #888;
}

.close-modal-btn:hover {
    color: #333;
}

.accordion-item { border-bottom: 1px solid var(--input-border); }
.accordion-item:last-child { border-bottom: none; }
.accordion-header { background: var(--field-bg); width: 100%; border: none; text-align: left; padding: 15px; font-size: 16px; font-weight: bold; cursor: pointer; transition: background-color 0.3s; display: flex; justify-content: space-between; align-items: center; }
.accordion-header:hover, .accordion-header.active { background-color: var(--main-bg); }
.accordion-header::after { content: '+'; font-size: 20px; color: var(--btn-bg); transition: transform 0.3s; }
.accordion-header.active::after { transform: rotate(45deg); }
.accordion-content-inner { max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background: var(--main-bg); padding: 0 15px; }
.accordion-content-inner p { margin: 10px 0; line-height: 1.6; }
.accordion-content-inner code {
    background: #e0e0e0; /* Lighter background for code in light mode */
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #333; /* Ensure dark text for light mode */
}

.dark-mode .accordion-content-inner code {
    background: var(--code-block-bg); /* Keep dark background for code in dark mode */
    color: #ffffff; /* Pure white for dark mode code snippets */
}
.example-box { background: var(--field-bg); border: 1px solid var(--input-border); padding: 15px; margin: 15px 0; border-radius: 8px; display: flex; justify-content: space-between; align-items: center; }
.try-it-btn { background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-weight: bold; transition: background 0.2s; }
.try-it-btn:hover { background: #229954; }

.project-switcher {
    display: inline-block;
    margin-left: 18px;
}
#project-switch-btn {
    background: #388bfd;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 6px 18px;
    font-size: 15px;
    cursor: pointer;
    font-family: inherit;
    transition: background 0.2s;
}
#project-switch-btn:hover {
    background: #2563eb;
}

.project-modal-content {
    min-width: 320px;
    max-width: 90vw;
    background: #23272e;
    color: #fff;
    border-radius: 10px;
    padding: 32px 24px 24px 24px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.22);
    position: relative;
}
#project-list {
    list-style: none;
    padding: 0;
    margin: 0 0 12px 0;
}
#project-list li {
    padding: 10px 0;
    border-bottom: 1px solid #333a;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.15s;
}
#project-list li.active {
    color: #388bfd;
    font-weight: bold;
}
#project-list li:hover {
    background: #2d333b;
}

.project-context-menu {
    display: none;
    position: absolute;
    z-index: 9999;
    background: #23272e;
    color: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
    min-width: 100px;
    padding: 6px 0;
    font-size: 15px;
    list-style: none;
    margin: 0;
}
.project-context-menu li {
    padding: 8px 18px;
    cursor: pointer;
    transition: background 0.15s;
}
.project-context-menu li:hover {
    background: #388bfd;
    color: #fff;
}

.project-badge {
    position: absolute;
    top: 64px;
    left: 3vw;
    background: #3a8bfd;
    color: #fff;
    font-size: 13px;
    font-weight: 600;
    border-radius: 0 8px 8px 0;
    padding: 4px 18px 4px 12px;
    box-shadow: 0 2px 8px #388bfd22;
    z-index: 10;
    pointer-events: none;
    letter-spacing: 0.5px;
    opacity: 0.92;
}

.code-title-row {
    padding: 32px 2vw 0 2vw;
}
.code-title-row h2 {
    margin: 0;
    font-size: 1.35em;
    font-weight: 700;
    color: #fff;
    letter-spacing: 1px;
}

.code-editor {
    width: 100%;
    height: 100%;
    min-height: 600px;
    background: #282c34;
    border: none;
    outline: none;
    font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'Menlo', monospace;
    font-size: 17px;
    color: #dcdfe4;
    line-height: 1.8;
    padding: 32px 48px 32px 48px;
    margin: 0;
    resize: none;
    box-sizing: border-box;
    flex: 1;
}

.code-editor::placeholder {
    color: #6a737d;
    font-style: italic;
}

.editor-toolbar {
    display: flex;
    align-items: center;
    background: #23272e;
    border-top: 1.5px solid #262b33;
    padding: 12px 32px;
    border-radius: 0 0 18px 18px;
    margin: 0;
}

@media (max-width: 900px) {
    .editor-toolbar {
        padding: 8px 8px;
        font-size: 13px;
    }
}

@media (max-width: 1200px) {
    .main-layout-3col { flex-direction: column; gap: 16px; }
    .code-section {
        max-width: 98vw;
    }
    .code-toolbar, .code-block {
        padding-left: 16px;
        padding-right: 16px;
        margin-left: 0;
        margin-right: 0;
    }
    .code-block {
        font-size: 15px;
    }
}

.code-header h2 {
    color: #fff;
    font-size: 1.35em;
    font-weight: 700;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.18);
    margin: 0;
    padding: 0 0 2px 0;
    border-left: 4px solid #388bfd;
    padding-left: 12px;
    background: linear-gradient(90deg, #388bfd22 0%, #21252b00 100%);
    border-radius: 4px;
    display: inline-block;
}

@media (max-width: 900px) {
    .code-card {
        max-width: 98vw;
        border-radius: 10px;
    }
    .framework-tabs, .code-block {
        padding-left: 8px;
        padding-right: 8px;
    }
    .code-block {
        font-size: 15px;
        padding: 32px 8px 32px 8px;
    }
}


