:root {
    --main-bg: #fafbfc;
    --main-text: #1a202c;
    --secondary-text: #4a5568;
    --header-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --header-text: #ffffff;
    --input-bg: #ffffff;
    --input-border: #e2e8f0;
    --input-focus: #4299e1;
    --field-bg: #ffffff;
    --field-border: #e2e8f0;
    --btn-bg: #4299e1;
    --btn-hover: #3182ce;
    --btn-secondary: #718096;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    --code-bg: #1a202c;
    --code-block-bg: #2d3748;
    --code-border: #4a5568;
    --modal-bg: #ffffff;
    --modal-overlay: rgba(0,0,0,0.5);
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.15);
    --border-radius: 8px;
    --border-radius-lg: 12px;
}
body.dark-mode {
    --main-bg: #0f1419;
    --main-text: #e2e8f0;
    --secondary-text: #a0aec0;
    --header-bg: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    --header-text: #ffffff;
    --input-bg: #1a202c;
    --input-border: #2d3748;
    --input-focus: #63b3ed;
    --field-bg: #1a202c;
    --field-border: #2d3748;
    --btn-bg: #4299e1;
    --btn-hover: #63b3ed;
    --btn-secondary: #4a5568;
    --success-color: #68d391;
    --warning-color: #fbb040;
    --error-color: #fc8181;
    --code-bg: #0d1117;
    --code-block-bg: #161b22;
    --code-border: #21262d;
    --modal-bg: #1a202c;
    --modal-overlay: rgba(0,0,0,0.8);
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.3);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.3);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.3);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.4);
}

.dark-mode .field-card {
    background: #303030;
    border-color: #505050;
}

.dark-mode .field-group label {
    color: #b0b0b0;
}

.dark-mode .field-group input,
.dark-mode .field-group select {
    background: #252525;
    border-color: #505050;
    color: #e0e0e0;
}

.dark-mode .results-table th {
    background-color: #303030;
}

.dark-mode .results-table tr:nth-child(even) {
    background-color: #282828;
}

.dark-mode .results-table td, .dark-mode .results-table th {
    border-color: #404040;
}

.dark-mode .main-col {
    background: var(--input-bg);
}

.dark-mode .form-group input, .dark-mode .form-group textarea, .dark-mode .form-group select {
    background: var(--field-bg);
    color: var(--main-text);
    border-color: var(--input-border);
}

.dark-mode .selector-input {
    background: #444;
    border-color: #ffc107;
}

.dark-mode .code-section {
    background: var(--code-bg);
    color: var(--header-text);
}

.dark-mode .framework-tab {
    background: var(--code-block-bg);
    color: white;
}

.dark-mode .framework-tab.active {
    background: var(--btn-bg);
}

.dark-mode .code-block {
    background: var(--code-block-bg);
    border-color: var(--code-border);
}

.dark-mode .results-container {
    background: var(--field-bg);
    border-color: var(--input-border);
}

.dark-mode .no-matches {
    color: #999;
}

.dark-mode .main-btn {
    background: var(--btn-bg);
}

.dark-mode .main-btn:hover {
    background: var(--btn-hover);
}

.dark-mode .btn-test {
    /* No changes needed, already has a gradient */
}

.dark-mode .remove-field-btn {
    /* No changes needed, already has a distinct color */
}

.dark-mode .add-field-btn-main {
    background: var(--btn-bg);
}

.dark-mode .add-field-btn-main:hover {
    background: var(--btn-hover);
}

.add-project-tab {
    background: #388bfd;
    color: #fff;
    border: none;
    border-radius: 10px;
    padding: 14px 38px;
    font-size: 22px;
    font-family: inherit;
    cursor: pointer;
    margin-left: 0;
    transition: background 0.2s;
}
.add-project-tab:hover {
    background: #2563eb;
}


* { margin: 0; padding: 0; box-sizing: border-box; }
body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif; background: var(--main-bg); color: var(--main-text); line-height: 1.6; }

/* 标题样式 */
h4 {
    margin: 0 0 20px 0;
    color: var(--main-text);
    font-size: 20px;
    font-weight: 700;
    position: relative;
    padding-bottom: 12px;
}

h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--btn-bg), var(--success-color));
    border-radius: 2px;
}

.input-section h4 {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-section h4::before {
    content: '⚙️';
    font-size: 18px;
}

.navbar {
    width: 100%;
    height: 64px;
    background: var(--header-bg);
    color: var(--header-text);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    z-index: 3000;
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.navbar-left .logo {
    font-size: 1.4em;
    font-weight: 700;
    letter-spacing: 0.5px;
    background: linear-gradient(45deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.project-info-panel {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.current-project-name {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
}

.project-stats {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: var(--header-text);
    font-size: 1.1em;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.nav-btn:hover:not(:disabled) {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.nav-btn:active:not(:disabled) {
    transform: translateY(0);
}

.main-layout-3col {
    display: flex;
    flex-direction: row;
    gap: 32px;
    max-width: 1800px;
    margin: 40px auto;
    padding: 0 32px;
}

/* 大屏优化 */
@media (min-width: 1600px) {
    .main-layout-3col {
        max-width: 2000px;
        gap: 40px;
        margin: 48px auto;
        padding: 0 40px;
    }

    .main-col {
        padding: 40px;
    }

    .framework-tab, .add-project-tab {
        padding: 14px 24px;
        font-size: 16px;
        min-width: 180px;
        max-width: 320px;
    }
}

@media (min-width: 2000px) {
    .main-layout-3col {
        max-width: 2400px;
        gap: 48px;
        margin: 56px auto;
        padding: 0 48px;
    }

    .main-col {
        padding: 48px;
    }
}

.main-col {
    background: var(--field-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 32px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--field-border);
    position: relative;
    overflow: hidden;
}

.main-col::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--btn-bg), var(--success-color));
    opacity: 0.8;
}

.html-col { flex: 1; min-width: 300px; }
.config-col { flex: 1.5; min-width: 450px; }
.result-col { flex: 1; min-width: 300px; }

.input-section { background: transparent; padding: 0; border: none; border-radius: 0; }
.code-section h2, .results-section h2 { color: var(--main-text); margin-bottom: 20px; font-size: 1.5em; }

.dark-mode .code-section h2,
.dark-mode .results-section h2 {
    color: #ffffff !important; /* Explicitly set to white in dark mode */
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--main-text);
    font-size: 14px;
    letter-spacing: 0.025em;
}

.form-group input, .form-group textarea, .form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: var(--field-bg);
    color: var(--main-text);
    box-shadow: var(--shadow-sm);
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    outline: none;
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    transform: translateY(-1px);
}

.form-group input:hover, .form-group textarea:hover, .form-group select:hover {
    border-color: var(--input-focus);
}
.html-col .input-section {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.html-col .form-group {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

#html-input {
    flex-grow: 1;
    resize: vertical;
    min-height: 60vh;
}

.selector-input { background: #fff3cd; border-color: #ffc107; transition: all 0.3s ease; }

/* 选择器验证状态 */
.selector-input.selector-error {
    border-color: #dc3545;
    background-color: #fff5f5;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.selector-input.selector-valid {
    border-color: #28a745;
    background-color: #f8fff8;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.1);
}

/* 验证提示样式 */
.validation-tip {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-top: 8px;
    padding: 10px;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    animation: slideIn 0.3s ease-out;
}

.validation-tip.error-tip {
    background: #fff5f5;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.validation-tip.success-tip {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

.tip-icon {
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 1px;
}

.tip-content {
    flex: 1;
}

.tip-suggestion {
    font-size: 12px;
    opacity: 0.8;
    font-style: italic;
}

/* 选择器错误显示 */
.selector-error {
    background: #fff5f5;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.error-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.error-content h4 {
    margin: 0 0 8px 0;
    color: #dc2626;
    font-size: 16px;
}

.error-content p {
    margin: 4px 0;
    color: #7f1d1d;
    font-size: 14px;
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 深色模式适配 */
.dark-mode .selector-input.selector-error {
    background-color: #2d1b1b;
    border-color: #dc3545;
}

.dark-mode .selector-input.selector-valid {
    background-color: #1b2d1b;
    border-color: #28a745;
}

.dark-mode .validation-tip.error-tip {
    background: #2d1b1b;
    border-color: #dc3545;
    color: #ff6b6b;
}

.dark-mode .validation-tip.success-tip {
    background: #1b2d1b;
    border-color: #28a745;
    color: #51cf66;
}

.dark-mode .selector-error {
    background: #2d1b1b;
    border-color: #dc3545;
}

.dark-mode .error-content h4 {
    color: #ff6b6b;
}

.dark-mode .error-content p {
    color: #fca5a5;
}

/* 小型验证提示（用于字段内的子选择器） */
.sub-validation-tip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
    margin-top: 2px;
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
    animation: slideIn 0.2s ease-out;
}

.error-tip-small {
    background: #fff5f5;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.dark-mode .error-tip-small {
    background: #2d1b1b;
    border-color: #dc3545;
    color: #ff6b6b;
}

/* 确保字段组有相对定位以支持绝对定位的提示 */
.field-group {
    position: relative;
}

/* 智能建议界面样式 */
.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background: #fafafa;
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    border-color: var(--btn-bg);
    background: #f0f8ff;
}

.suggestion-item.high {
    border-color: #28a745;
    background: #f8fff8;
}

.suggestion-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.suggestion-selector code {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    color: #495057;
}

.confidence-badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.confidence-badge.high {
    background: #d4edda;
    color: #155724;
}

.confidence-badge.medium {
    background: #fff3cd;
    color: #856404;
}

.suggestion-description {
    color: #666;
    font-size: 13px;
    margin-bottom: 10px;
}

.use-suggestion-btn {
    background: var(--btn-bg);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s;
}

.use-suggestion-btn:hover {
    background: var(--btn-hover);
}

/* 深色模式适配 */
.dark-mode .suggestion-item {
    background: #2d3748;
    border-color: #4a5568;
}

.dark-mode .suggestion-item:hover {
    background: #3d4852;
    border-color: var(--btn-bg);
}

.dark-mode .suggestion-item.high {
    background: #1b2d1b;
    border-color: #28a745;
}

.dark-mode .suggestion-selector code {
    background: #4a5568;
    color: #e2e8f0;
}

.dark-mode .suggestion-description {
    color: #a0aec0;
}

.dark-mode .confidence-badge.high {
    background: #28a745;
    color: white;
}

.dark-mode .confidence-badge.medium {
    background: #ffc107;
    color: #212529;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-layout-3col {
        max-width: 100%;
        gap: 16px;
        margin: 24px auto;
        padding: 0 16px;
    }

    .main-col {
        padding: 24px;
    }
}

@media (max-width: 768px) {
    .main-layout-3col {
        flex-direction: column;
        gap: 20px;
        margin: 16px auto;
        padding: 0 12px;
    }

    .main-col {
        padding: 20px;
    }

    .navbar {
        padding: 0 16px;
        height: 56px;
    }

    .navbar-left .logo {
        font-size: 1.2em;
    }

    .field-inputs {
        flex-direction: column;
        gap: 12px;
    }

    .field-group {
        min-width: 100%;
    }

    .results-stats {
        flex-direction: column;
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .main-col {
        padding: 16px;
    }

    .navbar {
        padding: 0 12px;
    }

    .field-card {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .field-inputs {
        order: 1;
    }

    .drag-handle {
        order: 2;
        align-self: center;
    }

    .remove-field-btn {
        order: 3;
        align-self: center;
    }
}

.btn-test {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 14px 32px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 16px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-test:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-test:active {
    transform: translateY(0);
}

.btn-test::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-test:hover::before {
    left: 100%;
}

.results-section { background: transparent; padding: 0; border: none; border-radius: 0; }
.results-container { background: var(--field-bg); border-radius: 8px; padding: 20px; border: 1px solid var(--input-border); max-height: 450px; overflow: auto; }
.no-matches { text-align: center; color: #666; padding: 40px; font-size: 16px; }
.no-matches::before { content: "😕"; display: block; font-size: 3em; margin-bottom: 15px; }

.code-section {
    margin: 48px 0 0 0;
    width: 100vw;
    background: #f5f7fa;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    padding: 0 0.05vw;
}

.code-card {
    margin: 0;
    width: 100%;
    max-width: 2400px;
    background: #23272e;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.13);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.code-toolbar {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 2vw;
    gap: 18px;
    border-bottom: 1.5px solid #282c34;
    min-height: 64px;
}

.framework-tabs {
    display: flex;
    gap: 8px;
    padding: 0 36px;
    min-height: 48px;
    background: #262b33;
    border-bottom: 1.5px solid #23272e;
}

.framework-tab, .add-project-tab {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: #e2e8f0;
    border: 2px solid transparent;
    border-radius: 12px 12px 0 0;
    padding: 12px 20px;
    font-size: 15px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    margin-bottom: 0;
    outline: none;
    box-shadow: var(--shadow-md);
    position: relative;
    min-width: 160px;
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-right: 4px;
}

.framework-tab:hover {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    transform: translateY(-2px);
    border-color: var(--input-focus);
    box-shadow: var(--shadow-lg);
}

.framework-tab.active {
    background: linear-gradient(135deg, var(--btn-bg) 0%, #5a67d8 100%);
    color: #ffffff;
    font-weight: 700;
    box-shadow: var(--shadow-xl);
    transform: translateY(-3px);
    border-color: var(--btn-bg);
}

.framework-tab.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--btn-bg), var(--success-color));
    border-radius: 2px;
}

.framework-tab.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

/* 工程状态指示器 */
.project-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--success-color);
    flex-shrink: 0;
    box-shadow: 0 0 0 2px rgba(72, 187, 120, 0.2);
    transition: all 0.3s ease;
}

.project-status.modified {
    background: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(237, 137, 54, 0.2);
    animation: pulse 2s infinite;
}

.project-status.error {
    background: var(--error-color);
    box-shadow: 0 0 0 2px rgba(245, 101, 101, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

/* 工程信息容器 */
.project-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    flex-grow: 1;
    min-width: 0;
}

.project-name {
    font-weight: 600;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.project-fields-count {
    font-size: 11px;
    opacity: 0.7;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 工程关闭按钮 */
.project-close-btn {
    opacity: 0;
    background: none;
    border: none;
    color: currentColor;
    font-size: 16px;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    transition: all 0.2s;
    flex-shrink: 0;
    margin-left: auto;
}

.framework-tab:hover .project-close-btn {
    opacity: 0.7;
}

.project-close-btn:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
}

.add-project-tab {
    background: #388bfd;
    color: #fff;
    padding: 8px 12px;
    min-width: 40px;
    justify-content: center;
}

.add-project-tab:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

/* 工程标签容器 */
#project-tabs {
    display: flex;
    align-items: flex-end;
    gap: 6px;
    margin-bottom: 0;
    padding: 0 8px;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--btn-bg) transparent;
    min-height: 60px;
}

#project-tabs::-webkit-scrollbar {
    height: 4px;
}

#project-tabs::-webkit-scrollbar-track {
    background: transparent;
}

#project-tabs::-webkit-scrollbar-thumb {
    background: var(--btn-bg);
    border-radius: 2px;
}

#project-tabs::-webkit-scrollbar-thumb:hover {
    background: var(--btn-hover);
}

.code-block {
    background: #282c34;
    border-radius: 0 0 18px 18px;
    margin: 0;
    padding: 0;
    min-height: 600px;
    max-height: none;
    box-shadow: none;
    border: none;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.copy-btn, .export-btn {
    background: #388bfd;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 8px 18px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s;
    box-shadow: 0 2px 8px rgba(56,139,253,0.10);
}
.copy-btn:hover, .export-btn:hover {
    background: #2563eb;
}
.export-btn {
    background: #28a745;
}
.export-btn:hover {
    background: #218838;
}

.code-block pre {
    margin: 0;
    background: none;
    border: none;
    padding: 0;
    overflow-x: auto;
}

.code-block code {
    background: none;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    white-space: pre;
    display: block;
}

.dark-mode .code-block pre {
    color: #ffffff;
}
.syntax-highlight { color: #e74c3c; } .string-highlight { color: #2ecc71; } .keyword-highlight { color: #3498db; } .comment-highlight { color: #95a5a6; }

/* 卡片式字段设计 */
#fields-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.field-card {
    background: var(--field-bg);
    border: 2px solid var(--field-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    padding: 20px;
    gap: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.field-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--btn-bg), var(--success-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.field-card:hover {
    border-color: var(--input-focus);
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.field-card:hover::before {
    opacity: 1;
}

.field-card.dragging {
    opacity: 0.8;
    box-shadow: var(--shadow-xl);
    transform: scale(1.02) rotate(2deg);
    cursor: grabbing;
    z-index: 10;
    border-color: var(--btn-bg);
}

.field-card.drag-over {
    border-color: var(--success-color);
    background: rgba(72, 187, 120, 0.05);
}

.drag-handle {
    font-size: 20px;
    color: var(--secondary-text);
    cursor: grab;
    user-select: none;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.drag-handle:hover {
    color: var(--btn-bg);
    background: rgba(66, 153, 225, 0.1);
}

.field-inputs {
    flex-grow: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.field-group {
    display: flex;
    flex-direction: column;
    flex: 1 1 140px;
    min-width: 140px;
    position: relative;
}

.field-group label {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 6px;
    color: var(--secondary-text);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.field-group input, .field-group select {
    padding: 10px 12px;
    font-size: 14px;
    border-radius: var(--border-radius);
    border: 2px solid var(--input-border);
    background: var(--field-bg);
    color: var(--main-text);
    transition: all 0.3s ease;
    font-family: 'SF Mono', 'Monaco', monospace;
}

.field-group input:focus, .field-group select:focus {
    outline: none;
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    transform: translateY(-1px);
}

.field-group input:hover, .field-group select:hover {
    border-color: var(--input-focus);
}

.remove-field-btn {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
    opacity: 0.8;
}

.remove-field-btn:hover {
    background: #e53e3e;
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
    opacity: 1;
}

.add-field-btn-main {
    display: block;
    width: 100%;
    padding: 16px 24px;
    margin-top: 20px;
    background: linear-gradient(135deg, var(--btn-bg) 0%, var(--success-color) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.add-field-btn-main:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--btn-hover) 0%, #38a169 100%);
}

.add-field-btn-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.add-field-btn-main:hover::before {
    left: 100%;
}

.field-templates { display: flex; flex-wrap: wrap; gap: 6px; align-items: center; }
.template-btn {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}
.template-btn:hover {
    background: var(--btn-bg);
    color: white;
    border-color: var(--btn-bg);
}
.dark-mode .template-btn {
    background: #404040;
    border-color: #555;
    color: #ccc;
}
.dark-mode .template-btn:hover {
    background: var(--btn-bg);
    color: white;
}

.main-btn { background: var(--btn-bg); color: #fff; border: none; border-radius: 6px; padding: 10px 0; font-size: 15px; font-weight: 500; width: 100%; margin-top: 10px; cursor: pointer; transition: background 0.2s; box-shadow: 0 2px 8px rgba(30, 136, 229, 0.08); display: flex; align-items: center; justify-content: center; gap: 6px; }
.main-btn:hover { background: var(--btn-hover); }

.results-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: var(--border-radius-lg);
    font-size: 14px;
    border: 1px solid var(--input-border);
    box-shadow: var(--shadow-sm);
}

.dark-mode .results-stats {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: #e2e8f0;
    border-color: var(--field-border);
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.stats-item strong {
    color: var(--btn-bg);
    font-size: 16px;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    margin-top: 16px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.results-table th, .results-table td {
    border: 1px solid var(--input-border);
    padding: 12px 16px;
    text-align: left;
}

.results-table th {
    background: linear-gradient(135deg, var(--btn-bg) 0%, #5a67d8 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 12px;
}

.results-table tr:nth-child(even) {
    background-color: rgba(66, 153, 225, 0.02);
}

.results-table tr:hover {
    background-color: rgba(66, 153, 225, 0.05);
}

.results-table td {
    word-break: break-all;
    font-family: 'SF Mono', 'Monaco', monospace;
    font-size: 13px;
}

.dark-mode .results-table th {
    background: linear-gradient(135deg, var(--btn-bg) 0%, #4c51bf 100%);
}

.dark-mode .results-table tr:nth-child(even) {
    background-color: rgba(66, 153, 225, 0.05);
}

.dark-mode .results-table tr:hover {
    background-color: rgba(66, 153, 225, 0.1);
}

.label-with-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.reference-btn {
    background: none;
    border: 1px solid var(--btn-bg);
    color: var(--btn-bg);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}

.reference-btn:hover {
    background: var(--btn-bg);
    color: white;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--modal-overlay);
    z-index: 1000;
    display: none; /* Hidden by default */
    align-items: center;
    justify-content: center;
}

.modal-overlay.active {
    display: flex; /* Show when active */
}

.modal-content {
    background: var(--modal-bg);
    padding: 30px;
    border-radius: 10px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.close-modal-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #888;
}

.close-modal-btn:hover {
    color: #333;
}

.accordion-item { border-bottom: 1px solid var(--input-border); }
.accordion-item:last-child { border-bottom: none; }
.accordion-header { background: var(--field-bg); width: 100%; border: none; text-align: left; padding: 15px; font-size: 16px; font-weight: bold; cursor: pointer; transition: background-color 0.3s; display: flex; justify-content: space-between; align-items: center; }
.accordion-header:hover, .accordion-header.active { background-color: var(--main-bg); }
.accordion-header::after { content: '+'; font-size: 20px; color: var(--btn-bg); transition: transform 0.3s; }
.accordion-header.active::after { transform: rotate(45deg); }
.accordion-content-inner { max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background: var(--main-bg); padding: 0 15px; }
.accordion-content-inner p { margin: 10px 0; line-height: 1.6; }
.accordion-content-inner code {
    background: #e0e0e0; /* Lighter background for code in light mode */
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #333; /* Ensure dark text for light mode */
}

.dark-mode .accordion-content-inner code {
    background: var(--code-block-bg); /* Keep dark background for code in dark mode */
    color: #ffffff; /* Pure white for dark mode code snippets */
}
.example-box { background: var(--field-bg); border: 1px solid var(--input-border); padding: 15px; margin: 15px 0; border-radius: 8px; display: flex; justify-content: space-between; align-items: center; }
.try-it-btn { background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-weight: bold; transition: background 0.2s; }
.try-it-btn:hover { background: #229954; }

.project-switcher {
    display: inline-block;
    margin-left: 18px;
}
#project-switch-btn {
    background: #388bfd;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 6px 18px;
    font-size: 15px;
    cursor: pointer;
    font-family: inherit;
    transition: background 0.2s;
}
#project-switch-btn:hover {
    background: #2563eb;
}

.project-modal-content {
    min-width: 320px;
    max-width: 90vw;
    background: #23272e;
    color: #fff;
    border-radius: 10px;
    padding: 32px 24px 24px 24px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.22);
    position: relative;
}
#project-list {
    list-style: none;
    padding: 0;
    margin: 0 0 12px 0;
}
#project-list li {
    padding: 10px 0;
    border-bottom: 1px solid #333a;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.15s;
}
#project-list li.active {
    color: #388bfd;
    font-weight: bold;
}
#project-list li:hover {
    background: #2d333b;
}

.project-context-menu {
    display: none;
    position: absolute;
    z-index: 9999;
    background: #23272e;
    color: #fff;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    min-width: 160px;
    padding: 8px 0;
    font-size: 14px;
    list-style: none;
    margin: 0;
    border: 1px solid #404040;
}

.project-context-menu li {
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.project-context-menu li:hover {
    background: #388bfd;
    color: #fff;
}

.menu-separator {
    height: 1px;
    background: #404040;
    margin: 4px 0;
    cursor: default;
}

.menu-separator:hover {
    background: #404040;
}

.dark-mode .project-context-menu {
    background: #1a1a1a;
    border-color: #333;
}

.dark-mode .menu-separator {
    background: #333;
}

.project-badge {
    position: absolute;
    top: 64px;
    left: 3vw;
    background: #3a8bfd;
    color: #fff;
    font-size: 13px;
    font-weight: 600;
    border-radius: 0 8px 8px 0;
    padding: 4px 18px 4px 12px;
    box-shadow: 0 2px 8px #388bfd22;
    z-index: 10;
    pointer-events: none;
    letter-spacing: 0.5px;
    opacity: 0.92;
}

.code-title-row {
    padding: 32px 2vw 0 2vw;
}
.code-title-row h2 {
    margin: 0;
    font-size: 1.35em;
    font-weight: 700;
    color: #fff;
    letter-spacing: 1px;
}

.code-editor {
    width: 100%;
    height: 100%;
    min-height: 600px;
    background: #282c34;
    border: none;
    outline: none;
    font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'Menlo', monospace;
    font-size: 17px;
    color: #dcdfe4;
    line-height: 1.8;
    padding: 32px 48px 32px 48px;
    margin: 0;
    resize: none;
    box-sizing: border-box;
    flex: 1;
}

.code-editor::placeholder {
    color: #6a737d;
    font-style: italic;
}

.editor-toolbar {
    display: flex;
    align-items: center;
    background: #23272e;
    border-top: 1.5px solid #262b33;
    padding: 12px 32px;
    border-radius: 0 0 18px 18px;
    margin: 0;
}

@media (max-width: 900px) {
    .editor-toolbar {
        padding: 8px 8px;
        font-size: 13px;
    }
}

@media (max-width: 1200px) {
    .main-layout-3col { flex-direction: column; gap: 16px; }
    .code-section {
        max-width: 98vw;
    }
    .code-toolbar, .code-block {
        padding-left: 16px;
        padding-right: 16px;
        margin-left: 0;
        margin-right: 0;
    }
    .code-block {
        font-size: 15px;
    }
}

.code-header h2 {
    color: #fff;
    font-size: 1.35em;
    font-weight: 700;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.18);
    margin: 0;
    padding: 0 0 2px 0;
    border-left: 4px solid #388bfd;
    padding-left: 12px;
    background: linear-gradient(90deg, #388bfd22 0%, #21252b00 100%);
    border-radius: 4px;
    display: inline-block;
}

@media (max-width: 900px) {
    .code-card {
        max-width: 98vw;
        border-radius: 10px;
    }
    .framework-tabs, .code-block {
        padding-left: 8px;
        padding-right: 8px;
    }
    .code-block {
        font-size: 15px;
        padding: 32px 8px 32px 8px;
    }
}


