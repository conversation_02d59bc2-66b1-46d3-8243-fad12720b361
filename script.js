let currentMode = 'multi'; // 默认多字段模式
let currentFramework = 'colly';
let historyStack = [];
let isRestoring = false;

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => { clearTimeout(timeout); func(...args); };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

document.addEventListener('DOMContentLoaded', () => {
    // Theme switcher logic
    const themeToggleButton = document.getElementById('theme-toggle-btn');
    const currentTheme = localStorage.getItem('theme');

    if (currentTheme === 'dark') {
        document.body.classList.add('dark-mode');
    }

    themeToggleButton.addEventListener('click', () => {
        document.body.classList.toggle('dark-mode');
        let theme = 'light';
        if (document.body.classList.contains('dark-mode')) {
            theme = 'dark';
        }
        localStorage.setItem('theme', theme);
    });

    // Modal logic
    const modal = document.getElementById('reference-modal');
    const openBtn = document.getElementById('open-reference-btn');
    const closeBtn = document.getElementById('close-modal-btn');

    openBtn.addEventListener('click', () => modal.classList.add('active'));
    closeBtn.addEventListener('click', () => modal.classList.remove('active'));
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('active');
        }
    });

    document.querySelectorAll('.accordion-header').forEach(header => {
        header.addEventListener('click', () => {
            const content = header.nextElementSibling;
            header.classList.toggle('active');
            if (content.style.maxHeight) {
                content.style.maxHeight = null;
            } else {
                content.style.maxHeight = content.scrollHeight + "px";
            }
        });
    });

    // 设置默认HTML
    document.getElementById('html-input').value = `
<div class="product-list">
    <div class="product-card" data-id="p001">
        <a href="/product/1">
            <img src="/images/product1.jpg" alt="Go语言编程">
            <h2 class="product-title">Go语言并发编程实战</h2>
        </a>
        <div class="price-box">
            <span class="price">¥79.00</span>
            <span class="original-price">¥99.00</span>
        </div>
        <div class="rating">4.8星</div>
    </div>
    <div class="product-card" data-id="p002">
        <a href="/product/2">
            <img src="/images/product2.jpg" alt="Python爬虫">
            <h2 class="product-title">Python网络爬虫权威指南</h2>
        </a>
        <div class="price-box">
            <span class="price">¥88.50</span>
        </div>
        <div class="rating">4.9星</div>
    </div>
    <div class="product-card" data-id="p003">
        <a href="/product/3">
            <img src="/images/product3.jpg" alt="算法导论">
            <h2 class="product-title">算法导论 (原书第3版)</h2>
        </a>
        <div class="price-box">
            <span class="price">¥129.00</span>
        </div>
        <div class="rating">5.0星</div>
    </div>
</div>`;
    
    // 默认设置
    document.getElementById('list-item-selector').value = '.product-card';
    // 默认字段将在Monaco Editor加载完成后添加

    // 实时匹配监听
    const debouncedTest = debounce(testSelector, 300);
    const debouncedValidation = debounce(validateSelectorInput, 200);

    const selectorInput = document.getElementById('list-item-selector');
    selectorInput.addEventListener('input', (e) => {
        debouncedValidation(e.target);
        debouncedTest();
    });

    document.getElementById('html-input').addEventListener('input', debouncedTest);
    // fields-container 的事件监听器在 mountFieldEvents() 中处理
    const addFieldBtn = document.getElementById('add-field-btn');
    if (addFieldBtn) {
        addFieldBtn.addEventListener('click', () => {
            saveStateToHistory(); // 保存状态用于撤销
            addField();
        });
    } else {
        console.error('找不到添加字段按钮元素');
    }

    // 格式化HTML按钮逻辑
    document.getElementById('format-html-btn').onclick = function() {
        const textarea = document.getElementById('html-input');
        try {
            textarea.value = html_beautify(textarea.value, { indent_size: 2, wrap_line_length: 120 });
        } catch (e) {
            alert('格式化失败，请检查HTML内容是否有误！');
        }
    };

    testSelector();
    renderProjectTabs();
    mountFieldEvents();

    // 保存和加载项目功能
    document.getElementById('save-project-btn').addEventListener('click', saveProjectToFile);
    document.getElementById('load-project-btn').addEventListener('click', () => {
        document.getElementById('load-project-file').click();
    });
    document.getElementById('load-project-file').addEventListener('change', loadProjectFromFile);

    // 撤销功能
    document.getElementById('undo-btn').addEventListener('click', undoLastAction);

    // 智能建议功能
    document.getElementById('suggest-selector-btn').addEventListener('click', suggestSelectors);

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        // Ctrl+Z 撤销
        if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            undoLastAction();
        }
        // Ctrl+S 保存项目
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveProjectToFile();
        }
        // Ctrl+Enter 添加字段
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            addField();
        }
        // F5 刷新测试
        if (e.key === 'F5') {
            e.preventDefault();
            testSelector();
        }
        // Ctrl+T 新建工程
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            createNewProject();
        }
        // Ctrl+W 关闭当前工程
        if (e.ctrlKey && e.key === 'w') {
            e.preventDefault();
            if (projects.length > 1) {
                closeProject(currentProjectIndex);
            }
        }
        // Ctrl+Tab 切换到下一个工程
        if (e.ctrlKey && e.key === 'Tab') {
            e.preventDefault();
            const nextIndex = (currentProjectIndex + 1) % projects.length;
            saveCurrentProject();
            switchProject(nextIndex);
        }
    });
});

require.config({ paths: { 'vs': 'https://unpkg.com/monaco-editor@0.44.0/min/vs' }});
let monacoEditor;

// 1. 将 goDemoCode 提前到顶部
const goDemoCode = `package main\n\nimport (\n    \"fmt\"\n    \"github.com/gocolly/colly\"\n)\n\ntype ProductItem struct {\n    Url string \`colly:\"selector:a attr:href\"\`\n}\n\nfunc main() {\n    // 实例化 Colly 爬虫\n    c := colly.NewCollector(\n        colly.AllowedDomains(\"example.com\"),\n    )\n\n    // 解析页面元素\n    c.OnHTML(\".product-card\", func(e *colly.HTMLElement) {\n        item := &ProductItem{}\n        err := e.Unmarshal(item)\n        if err != nil {\n            fmt.Println(\"解析失败:\", err)\n            return\n        }\n        fmt.Printf(\"抓取到商品: %+v\\n\", item)\n    })\n\n    c.Visit(\"https://example.com/products\")\n}\n`;

// 1. 工程对象增加 html、selector、fields 字段
let projects = [
  { name: "默认工程", code: goDemoCode, language: "go", html: '', selector: '', fields: [] }
];
let currentProjectIndex = 0;

function renderProjectTabs() {
  const tabs = document.getElementById('project-tabs');
  tabs.innerHTML = '';

  projects.forEach((p, i) => {
    const btn = document.createElement('button');
    btn.className = 'framework-tab' + (i === currentProjectIndex ? ' active' : '');
    btn.title = `${p.name}\n点击切换，右键更多选项`;

    // 状态指示器
    const statusDot = document.createElement('div');
    statusDot.className = 'project-status';
    if (p.hasUnsavedChanges) {
      statusDot.className += ' modified';
      statusDot.title = '有未保存的更改';
    } else if (p.hasError) {
      statusDot.className += ' error';
      statusDot.title = '存在错误';
    } else {
      statusDot.title = '已保存';
    }

    // 工程名称
    const nameSpan = document.createElement('span');
    nameSpan.textContent = p.name;
    nameSpan.className = 'project-name';

    // 关闭按钮（仅在有多个工程时显示）
    const closeBtn = document.createElement('button');
    closeBtn.className = 'project-close-btn';
    closeBtn.innerHTML = '×';
    closeBtn.title = '关闭工程';
    closeBtn.onclick = (e) => {
      e.stopPropagation();
      closeProject(i);
    };

    btn.appendChild(statusDot);
    btn.appendChild(nameSpan);
    if (projects.length > 1) {
      btn.appendChild(closeBtn);
    }

    // 点击切换工程
    btn.onclick = (e) => {
      if (e.target === closeBtn) return; // 防止关闭按钮触发切换
      if (i !== currentProjectIndex) {
        saveCurrentProject();
        switchProject(i);
      }
    };

    // 右键菜单
    btn.oncontextmenu = (e) => {
      e.preventDefault();
      showProjectContextMenu(e, i);
    };

    // 双击重命名
    btn.ondblclick = (e) => {
      e.preventDefault();
      renameProject(i);
    };

    tabs.appendChild(btn);
  });

  // 新建工程按钮
  const addBtn = document.createElement('button');
  addBtn.className = 'add-project-tab';
  addBtn.innerHTML = '+';
  addBtn.title = '新建工程 (Ctrl+T)';
  addBtn.onclick = function() {
    createNewProject();
  };
  tabs.appendChild(addBtn);
}

// 2. 保存当前工程所有配置
function saveCurrentProject() {
  if (projects[currentProjectIndex]) {
    const p = projects[currentProjectIndex];

    // 只有在Monaco Editor加载完成后才保存代码
    if (monacoEditor) {
      p.code = monacoEditor.getValue();
    }

    const langSelect = document.getElementById('language-select');
    if (langSelect) {
      p.language = langSelect.value;
    }

    const htmlInput = document.getElementById('html-input');
    if (htmlInput) {
      p.html = htmlInput.value;
    }

    const selectorInput = document.getElementById('list-item-selector');
    if (selectorInput) {
      p.selector = selectorInput.value;
    }

    p.fields = getFields();

    // 标记为已保存
    p.hasUnsavedChanges = false;

    // 检查是否有错误
    p.hasError = checkProjectErrors(p);

    // 更新标签显示
    renderProjectTabs();
  }
}

// 检查工程是否有错误
function checkProjectErrors(project) {
  // 检查选择器语法
  if (project.selector) {
    const validation = validateSelector(project.selector);
    if (!validation.isValid) return true;
  }

  // 检查字段选择器
  if (project.fields) {
    for (const field of project.fields) {
      if (field.selector) {
        const validation = validateSelector(field.selector);
        if (!validation.isValid) return true;
      }
    }
  }

  return false;
}

// 标记工程有未保存更改
function markProjectAsModified() {
  if (projects[currentProjectIndex]) {
    projects[currentProjectIndex].hasUnsavedChanges = true;
    renderProjectTabs();
  }
}

// 3. 切换工程时恢复所有配置
function switchProject(index) {
  currentProjectIndex = index;
  renderProjectTabs();
  const p = projects[index];
  if (monacoEditor) {
    monacoEditor.setValue(p.code || '');
    monaco.editor.setModelLanguage(monacoEditor.getModel(), p.language || 'go');
  }
  const langSelect = document.getElementById('language-select');
  if (langSelect) {
    langSelect.value = p.language || 'go';
  }
  document.getElementById('html-input').value = p.html || '';
  document.getElementById('list-item-selector').value = p.selector || '';
  document.getElementById('fields-container').innerHTML = '';
  if (p.fields && p.fields.length) {
    p.fields.forEach(f => addField(f.name, f.selector, f.type, f.attribute));
  }
  testSelector();
}

require(['vs/editor/editor.main'], function() {
  monacoEditor = monaco.editor.create(document.getElementById('editor'), {
    value: projects[0].code,
    language: projects[0].language,
    theme: 'vs-dark',
    fontSize: 17,
    minimap: { enabled: false }
  });
  // 监听下拉框切换语言
  const langSelect = document.getElementById('language-select');
  if(langSelect) {
    langSelect.addEventListener('change', function() {
      monaco.editor.setModelLanguage(monacoEditor.getModel(), this.value);
      if (projects[currentProjectIndex]) {
        projects[currentProjectIndex].language = this.value;
      }
    });
  }
  // 编辑器内容变动时，自动保存到当前工程
  monacoEditor.onDidChangeModelContent(function() {
    if (projects[currentProjectIndex]) {
      projects[currentProjectIndex].code = monacoEditor.getValue();
    }
  });
  renderProjectTabs();

  // 添加默认字段（在Monaco Editor加载完成后）
  if (projects[currentProjectIndex] && (!projects[currentProjectIndex].fields || projects[currentProjectIndex].fields.length === 0)) {
    addField('title', 'h2.product-title', 'text');
    addField('price', '.price', 'text');
    addField('url', 'a', 'attribute', 'href');
  }
});

function addField(name = '', selector = '', type = 'text', attr = '') {
    const template = document.getElementById('field-card-template');
    if (!template) {
        console.error('找不到字段模板元素');
        return;
    }
    const clone = template.content.cloneNode(true);
    const fieldCard = clone.querySelector('.field-card');

    if (!fieldCard) {
        console.error('找不到字段卡片元素');
        return;
    }

    // 安全地设置字段值
    const fieldNameInput = fieldCard.querySelector('.field-name');
    const subSelectorInput = fieldCard.querySelector('.sub-selector');
    const extractTypeSelect = fieldCard.querySelector('.extract-type');

    if (fieldNameInput) fieldNameInput.value = name;
    if (subSelectorInput) subSelectorInput.value = selector;
    if (extractTypeSelect) extractTypeSelect.value = type;
    const attrGroup = fieldCard.querySelector('.attr-name-group');
    const attrInput = fieldCard.querySelector('.attr-name');
    if (attrGroup && attrInput) {
        if (type === 'attribute') {
            attrGroup.style.display = 'flex';
            attrInput.value = attr;
        } else {
            attrGroup.style.display = 'none';
        }
    }

    const removeBtn = fieldCard.querySelector('.remove-field-btn');
    if (removeBtn) {
        removeBtn.onclick = () => {
            saveStateToHistory(); // 保存状态用于撤销
            fieldCard.remove();
            saveCurrentProject();
            testSelector();
        };
    }

    if (extractTypeSelect && attrGroup) {
        extractTypeSelect.onchange = (e) => {
            attrGroup.style.display = e.target.value === 'attribute' ? 'flex' : 'none';
            saveCurrentProject();
            testSelector();
        };
    }

    // 为新字段添加输入事件监听器
    if (fieldNameInput) {
        fieldNameInput.addEventListener('input', () => {
            saveCurrentProject();
            testSelector();
        });
    }
    if (subSelectorInput) {
        subSelectorInput.addEventListener('input', (e) => {
            validateSubSelector(e.target);
            saveCurrentProject();
            testSelector();
        });
    }
    if (attrInput) {
        attrInput.addEventListener('input', () => {
            saveCurrentProject();
            testSelector();
        });
    }

    // 拖拽逻辑
    fieldCard.addEventListener('dragstart', handleDragStart);
    fieldCard.addEventListener('dragover', handleDragOver);
    fieldCard.addEventListener('dragleave', handleDragLeave);
    fieldCard.addEventListener('drop', handleDrop);
    fieldCard.addEventListener('dragend', handleDragEnd);

    const fieldsContainer = document.getElementById('fields-container');
    if (!fieldsContainer) {
        console.error('找不到字段容器元素');
        return;
    }
    fieldsContainer.appendChild(clone);
    saveCurrentProject();
    testSelector();
}

let draggedItem = null;

function handleDragStart(e) {
    draggedItem = this;
    setTimeout(() => {
        this.classList.add('dragging');
    }, 0);
}

function handleDragOver(e) {
    e.preventDefault();
    const container = document.getElementById('fields-container');
    const afterElement = getDragAfterElement(container, e.clientY);
    if (afterElement == null) {
        container.appendChild(draggedItem);
    } else {
        container.insertBefore(draggedItem, afterElement);
    }
}

function handleDragLeave(e) {
    this.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
}

function handleDragEnd(e) {
    this.classList.remove('dragging');
    testSelector();
}

function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.field-card:not(.dragging)')];

    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}


function clearResults() {
    document.getElementById('results-container').innerHTML = '<div class="no-matches">输入信息后，结果将实时更新</div>';
    // 清空Monaco编辑器中的代码
    if (monacoEditor) {
        monacoEditor.setValue('// 输入信息后，代码将实时更新');
    }
}

function testSelector() {
    testMultiField();
}

function testMultiField() {
    const listItemSelector = document.getElementById('list-item-selector').value;
    const html = document.getElementById('html-input').value;
    const fieldCards = document.querySelectorAll('#fields-container .field-card');
    if (!listItemSelector || !html || fieldCards.length === 0) { clearResults(); return; }
    const fields = getFields();
    if (fields.length === 0) { clearResults(); return; }

    // 验证选择器语法
    const selectorValidation = validateSelector(listItemSelector);
    if (!selectorValidation.isValid) {
        document.getElementById('results-container').innerHTML = `
            <div class="selector-error">
                <div class="error-icon">❌</div>
                <div class="error-content">
                    <h4>选择器语法错误</h4>
                    <p><strong>错误:</strong> ${selectorValidation.error}</p>
                    <p><strong>建议:</strong> ${selectorValidation.suggestion}</p>
                </div>
            </div>`;
        return;
    }
    const doc = new DOMParser().parseFromString(html, 'text/html');
    const listItems = doc.querySelectorAll(listItemSelector);
    const resultsData = [];
    listItems.forEach(item => {
        const rowData = {};
        fields.forEach(field => {
            const targetElement = field.selector ? item.querySelector(field.selector) : item;
            if (targetElement) {
                if (field.type === 'text') { rowData[field.name] = targetElement.textContent.trim(); } 
                else if (field.type === 'attribute' && field.attribute) { rowData[field.name] = targetElement.getAttribute(field.attribute); }
            } else { rowData[field.name] = null; }
        });
        resultsData.push(rowData);
    });
    renderMultiFieldResults(resultsData, fields);
    generateCode();
}

function renderMultiFieldResults(data, fields) {
    if (data.length === 0) { document.getElementById('results-container').innerHTML = '<div class="no-matches">未找到匹配的列表项</div>'; return; }

    // 添加统计信息
    const statsDiv = document.createElement('div');
    statsDiv.className = 'results-stats';
    statsDiv.innerHTML = `
        <div class="stats-item">📊 找到 <strong>${data.length}</strong> 个匹配项</div>
        <div class="stats-item">🏷️ 提取 <strong>${fields.length}</strong> 个字段</div>
    `;

    const table = document.createElement('table');
    table.className = 'results-table';
    const thead = table.createTHead();
    const headerRow = thead.insertRow();
    fields.forEach(field => { const th = document.createElement('th'); th.textContent = field.name; headerRow.appendChild(th); });
    const tbody = table.createTBody();
    data.forEach(rowData => {
        const row = tbody.insertRow();
        fields.forEach(field => { const cell = row.insertCell(); cell.textContent = rowData[field.name] ?? 'N/A'; });
    });
    const container = document.getElementById('results-container');
    container.innerHTML = '';
    container.appendChild(statsDiv);
    container.appendChild(table);
}

function generateCode() {
    const generator = codeGenerators[currentFramework];
    if (generator && monacoEditor) {
        const generatedCode = generator();
        monacoEditor.setValue(generatedCode);
        // 保存到当前项目
        if (projects[currentProjectIndex]) {
            projects[currentProjectIndex].code = generatedCode;
        }
    }
}

const codeGenerators = {
    colly: () => {
        const listItemSelector = document.getElementById('list-item-selector').value;
        const fields = getFields();
        if (!listItemSelector || fields.length === 0) return '<span class="comment-highlight">// 请输入列表项选择器并添加字段</span>';

        const structName = "ProductItem";
        let structFields = "";
        fields.forEach(f => {
            const fieldName = f.name.charAt(0).toUpperCase() + f.name.slice(1);
            let tag = `selector:"${f.selector || "."}"`;
            if (f.type === 'attribute') { tag += ` attr:"${f.attribute}"`; }
            structFields += `    ${fieldName} string \`colly:"${tag}"\`\n`;
        });

        return `package main\n\nimport (\n\t"fmt"\n\t"log"\n\n\t"github.com/gocolly/colly"\n)\n\n// ${structName} represents the structure of a product item\ntype ${structName} struct {\n${structFields}}\n\nfunc main() {\n\t// Instantiate a new Colly collector\n\tc := colly.NewCollector(\n\t\t// Visit only domains: example.com\n\t\tcolly.AllowedDomains("example.com"),\n\t)\n\n\t// On every a element which has href attribute call callback\n\tc.OnHTML("${listItemSelector}", func(e *colly.HTMLElement) {\n\t\titem := &${structName}{}\n\t\t// Unmarshal the HTML element into the struct\n\t\terr := e.Unmarshal(item)\n\t\tif err != nil {\n\t\t\tlog.Printf("Error unmarshaling item: %v", err)\n\t\t\treturn\n\t\t}\n\t\tfmt.Printf("Found Item: %+v\n", item)\n\t})\n\n\t// Start scraping on https://example.com\n\tlog.Println("Visiting https://example.com...")\n\t err := c.Visit("https://example.com")\n\t if err != nil {\n\t\tlog.Fatalf("Error visiting URL: %v", err)\n\t }\n\n\tlog.Println("Scraping finished.")\n}`;
    },
};

function getFields() {
    return Array.from(document.querySelectorAll('#fields-container .field-card')).map(card => ({ 
        name: card.querySelector('.field-name').value.trim(), 
        selector: card.querySelector('.sub-selector').value.trim(), 
        type: card.querySelector('.extract-type').value, 
        attribute: card.querySelector('.attr-name') ? card.querySelector('.attr-name').value.trim() : ''
    })).filter(f => f.name);
}

function switchFramework(framework) {
    currentFramework = framework;
    document.querySelectorAll('.framework-tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');
    generateCode();
}

function copyCode() {
    if (monacoEditor) {
        const text = monacoEditor.getValue();
        navigator.clipboard.writeText(text).then(() => {
            const btn = document.querySelector('.copy-btn');
            if (btn) {
                btn.textContent = '✅ 已复制';
                setTimeout(() => { btn.textContent = '📋 复制代码'; }, 2000);
            }
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动复制代码');
        });
    }
}

function exportCode() {
    if (monacoEditor) {
        const code = monacoEditor.getValue();
        const language = document.getElementById('language-select').value;
        const projectName = projects[currentProjectIndex]?.name || '未命名工程';

        // 根据语言确定文件扩展名
        const extensions = {
            'go': '.go',
            'javascript': '.js',
            'python': '.py',
            'json': '.json',
            'html': '.html',
            'css': '.css',
            'markdown': '.md'
        };

        const extension = extensions[language] || '.txt';
        const filename = `${projectName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}${extension}`;

        const blob = new Blob([code], { type: 'text/plain;charset=utf-8' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();

        URL.revokeObjectURL(link.href);

        // 更新按钮状态
        const btn = document.querySelector('.export-btn');
        if (btn) {
            btn.textContent = '✅ 已导出';
            setTimeout(() => { btn.textContent = '💾 导出文件'; }, 2000);
        }
    }
}

function escapeHtml(unsafe) {
    return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
}

// 选择器验证函数
function validateSelector(selector) {
    if (!selector || selector.trim() === '') {
        return {
            isValid: false,
            error: '选择器不能为空',
            suggestion: '请输入一个有效的CSS选择器，例如: .class-name 或 #id-name'
        };
    }

    try {
        // 基本语法验证
        document.querySelector(selector);

        // 检查常见错误模式
        const commonErrors = [
            {
                pattern: /^[0-9]/,
                error: '选择器不能以数字开头',
                suggestion: '请使用字母或下划线开头，例如: .item-1 而不是 .1-item'
            },
            {
                pattern: /\s{2,}/,
                error: '选择器包含多余的空格',
                suggestion: '请移除多余的空格，使用单个空格分隔选择器'
            },
            {
                pattern: /[^\w\s\-\.\#\[\]\:\(\)\>\+\~\*\=\^|\$"']/,
                error: '选择器包含无效字符',
                suggestion: '请只使用有效的CSS选择器字符'
            }
        ];

        for (const errorCheck of commonErrors) {
            if (errorCheck.pattern.test(selector)) {
                return {
                    isValid: false,
                    error: errorCheck.error,
                    suggestion: errorCheck.suggestion
                };
            }
        }

        return { isValid: true };
    } catch (e) {
        // 根据错误类型提供具体建议
        let suggestion = '请检查选择器语法';

        if (e.message.includes('not a valid selector')) {
            suggestion = '选择器语法无效，请参考CSS选择器规范';
        } else if (e.message.includes('unexpected token')) {
            suggestion = '选择器包含意外的字符，请检查括号、引号是否匹配';
        } else if (e.message.includes('unterminated')) {
            suggestion = '选择器未正确结束，请检查是否缺少闭合括号或引号';
        }

        return {
            isValid: false,
            error: e.message,
            suggestion: suggestion
        };
    }
}

// 实时验证选择器输入
function validateSelectorInput(inputElement) {
    const selector = inputElement.value.trim();
    const validation = validateSelector(selector);

    // 移除之前的验证提示
    const existingTip = inputElement.parentNode.querySelector('.validation-tip');
    if (existingTip) {
        existingTip.remove();
    }

    // 重置输入框样式
    inputElement.classList.remove('selector-error', 'selector-valid');

    if (selector === '') {
        return; // 空值时不显示提示
    }

    if (!validation.isValid) {
        // 显示错误状态
        inputElement.classList.add('selector-error');

        // 创建错误提示
        const tip = document.createElement('div');
        tip.className = 'validation-tip error-tip';
        tip.innerHTML = `
            <div class="tip-icon">⚠️</div>
            <div class="tip-content">
                <strong>${validation.error}</strong><br>
                <span class="tip-suggestion">${validation.suggestion}</span>
            </div>
        `;

        inputElement.parentNode.appendChild(tip);
    } else {
        // 显示成功状态
        inputElement.classList.add('selector-valid');

        // 创建成功提示
        const tip = document.createElement('div');
        tip.className = 'validation-tip success-tip';
        tip.innerHTML = `
            <div class="tip-icon">✅</div>
            <div class="tip-content">选择器语法正确</div>
        `;

        inputElement.parentNode.appendChild(tip);

        // 3秒后自动隐藏成功提示
        setTimeout(() => {
            if (tip.parentNode) {
                tip.remove();
            }
        }, 3000);
    }
}

// 验证子选择器
function validateSubSelector(inputElement) {
    const selector = inputElement.value.trim();

    // 移除之前的验证提示
    const existingTip = inputElement.parentNode.querySelector('.sub-validation-tip');
    if (existingTip) {
        existingTip.remove();
    }

    // 重置输入框样式
    inputElement.classList.remove('selector-error', 'selector-valid');

    if (selector === '') {
        return; // 空值时不显示提示
    }

    const validation = validateSelector(selector);

    if (!validation.isValid) {
        // 显示错误状态
        inputElement.classList.add('selector-error');

        // 创建错误提示（更小的样式）
        const tip = document.createElement('div');
        tip.className = 'sub-validation-tip error-tip-small';
        tip.innerHTML = `⚠️ ${validation.error}`;

        inputElement.parentNode.appendChild(tip);
    } else {
        // 显示成功状态
        inputElement.classList.add('selector-valid');
    }
}

// 智能建议选择器
function suggestSelectors() {
    const html = document.getElementById('html-input').value;
    if (!html.trim()) {
        alert('请先输入HTML代码');
        return;
    }

    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        const suggestions = analyzeHTMLStructure(doc);

        if (suggestions.length === 0) {
            alert('未找到合适的选择器建议');
            return;
        }

        showSelectorSuggestions(suggestions);
    } catch (error) {
        alert('HTML解析失败，请检查HTML格式');
    }
}

// 分析HTML结构并生成建议
function analyzeHTMLStructure(doc) {
    const suggestions = [];
    const elements = doc.querySelectorAll('*');
    const classCount = {};
    const tagCount = {};

    // 统计类名和标签使用频率
    elements.forEach(el => {
        const tagName = el.tagName.toLowerCase();
        tagCount[tagName] = (tagCount[tagName] || 0) + 1;

        if (el.className && typeof el.className === 'string') {
            el.className.split(/\s+/).forEach(className => {
                if (className.trim()) {
                    classCount[className] = (classCount[className] || 0) + 1;
                }
            });
        }
    });

    // 生成基于类名的建议（出现2次以上的类名）
    Object.entries(classCount).forEach(([className, count]) => {
        if (count >= 2 && count <= 10) { // 合理的重复次数
            suggestions.push({
                selector: `.${className}`,
                description: `类选择器 (${count} 个匹配项)`,
                confidence: count <= 5 ? 'high' : 'medium'
            });
        }
    });

    // 生成基于标签的建议
    Object.entries(tagCount).forEach(([tagName, count]) => {
        if (count >= 2 && count <= 20 && !['div', 'span', 'p'].includes(tagName)) {
            suggestions.push({
                selector: tagName,
                description: `标签选择器 (${count} 个匹配项)`,
                confidence: 'medium'
            });
        }
    });

    // 生成组合选择器建议
    const commonPatterns = [
        'article', 'section', 'li', 'tr', 'card', 'item', 'product', 'post'
    ];

    commonPatterns.forEach(pattern => {
        const matches = doc.querySelectorAll(`[class*="${pattern}"]`);
        if (matches.length >= 2 && matches.length <= 10) {
            suggestions.push({
                selector: `[class*="${pattern}"]`,
                description: `属性选择器 (${matches.length} 个匹配项)`,
                confidence: 'medium'
            });
        }
    });

    // 按置信度和匹配数量排序
    return suggestions
        .sort((a, b) => {
            const confidenceOrder = { high: 3, medium: 2, low: 1 };
            return confidenceOrder[b.confidence] - confidenceOrder[a.confidence];
        })
        .slice(0, 8); // 最多显示8个建议
}

// 显示选择器建议
function showSelectorSuggestions(suggestions) {
    const modal = document.getElementById('reference-modal');
    const modalContent = modal.querySelector('.modal-content');

    // 创建建议内容
    const suggestionsHTML = `
        <button id="close-suggestions-btn" class="close-modal-btn">&times;</button>
        <h2>🔍 智能选择器建议</h2>
        <p style="color: #666; margin-bottom: 20px;">基于您的HTML结构分析，以下是推荐的选择器：</p>
        <div class="suggestions-list">
            ${suggestions.map(suggestion => `
                <div class="suggestion-item ${suggestion.confidence}">
                    <div class="suggestion-selector">
                        <code>${suggestion.selector}</code>
                        <span class="confidence-badge ${suggestion.confidence}">${suggestion.confidence === 'high' ? '推荐' : '可选'}</span>
                    </div>
                    <div class="suggestion-description">${suggestion.description}</div>
                    <button class="use-suggestion-btn" onclick="useSuggestion('${suggestion.selector.replace(/'/g, "\\'")}')">使用此选择器</button>
                </div>
            `).join('')}
        </div>
        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; color: #666; font-size: 13px;">
            💡 提示：点击"使用此选择器"将自动填入选择器输入框
        </div>
    `;

    modalContent.innerHTML = suggestionsHTML;
    modal.classList.add('active');

    // 绑定关闭事件
    document.getElementById('close-suggestions-btn').addEventListener('click', () => {
        modal.classList.remove('active');
    });
}

// 使用建议的选择器
function useSuggestion(selector) {
    document.getElementById('list-item-selector').value = selector;
    document.getElementById('reference-modal').classList.remove('active');

    // 触发验证和测试
    const selectorInput = document.getElementById('list-item-selector');
    validateSelectorInput(selectorInput);
    testSelector();
}

// 试用选择器函数 - 用于参考手册中的"试用"按钮
function trySelector(selector) {
    document.getElementById('list-item-selector').value = selector;
    testSelector();
    // 关闭模态窗口
    document.getElementById('reference-modal').classList.remove('active');
}

// 创建新工程
function createNewProject() {
    saveCurrentProject();

    // 生成唯一的工程名
    let newName = '未命名工程';
    let counter = 1;
    while (projects.some(p => p.name === newName)) {
        newName = `未命名工程 ${counter}`;
        counter++;
    }

    const newProject = {
        name: newName,
        code: '',
        language: 'go',
        html: '',
        selector: '',
        fields: [],
        hasUnsavedChanges: false,
        hasError: false
    };

    projects.push(newProject);
    switchProject(projects.length - 1);
    renderProjectTabs();

    // 自动聚焦到工程名进行重命名
    setTimeout(() => {
        renameProject(currentProjectIndex);
    }, 100);
}

// 关闭工程
function closeProject(index) {
    if (projects.length <= 1) {
        alert('至少需要保留一个工程！');
        return;
    }

    const project = projects[index];

    // 检查是否有未保存的更改
    if (project.hasUnsavedChanges) {
        const confirmed = confirm(`工程 "${project.name}" 有未保存的更改，确定要关闭吗？`);
        if (!confirmed) return;
    }

    saveStateToHistory(); // 保存状态用于撤销

    projects.splice(index, 1);

    // 调整当前工程索引
    if (currentProjectIndex >= projects.length) {
        currentProjectIndex = projects.length - 1;
    } else if (currentProjectIndex > index) {
        currentProjectIndex--;
    }

    switchProject(currentProjectIndex);
    renderProjectTabs();
}

// 重命名工程
function renameProject(index) {
    const project = projects[index];
    const newName = prompt('请输入新的工程名称：', project.name);

    if (newName && newName.trim() && newName.trim() !== project.name) {
        const trimmedName = newName.trim();

        // 检查名称是否已存在
        if (projects.some((p, i) => i !== index && p.name === trimmedName)) {
            alert('工程名称已存在，请使用其他名称！');
            return;
        }

        project.name = trimmedName;
        project.hasUnsavedChanges = true;
        renderProjectTabs();
    }
}

// 复制工程
function duplicateProject(index) {
    saveCurrentProject();

    const originalProject = projects[index];
    let newName = `${originalProject.name} - 副本`;
    let counter = 1;

    while (projects.some(p => p.name === newName)) {
        newName = `${originalProject.name} - 副本 ${counter}`;
        counter++;
    }

    const duplicatedProject = {
        ...JSON.parse(JSON.stringify(originalProject)), // 深拷贝
        name: newName,
        hasUnsavedChanges: true
    };

    projects.push(duplicatedProject);
    switchProject(projects.length - 1);
    renderProjectTabs();
}

// 工程管理功能（按钮版）- 移除重复定义

// 右键菜单逻辑
let contextProjectIndex = null;
function showProjectContextMenu(e, projectIndex) {
    const menu = document.getElementById('project-context-menu');
    contextProjectIndex = projectIndex;
    menu.style.display = 'block';
    menu.style.left = e.pageX + 'px';
    menu.style.top = e.pageY + 'px';
}
// 隐藏菜单
window.addEventListener('click', function() {
    document.getElementById('project-context-menu').style.display = 'none';
});
// 右键菜单事件处理
const deleteProjectBtn = document.getElementById('delete-project');
if (deleteProjectBtn) {
    deleteProjectBtn.onclick = function() {
        if (projects.length === 1) {
            alert('至少保留一个工程！');
            return;
        }
        const projectName = projects[contextProjectIndex].name;
        if (confirm(`确定要删除工程 "${projectName}" 吗？此操作不可撤销！`)) {
            saveStateToHistory(); // 保存状态用于撤销
            projects.splice(contextProjectIndex, 1);
            if (currentProjectIndex >= projects.length) currentProjectIndex = projects.length - 1;
            if (currentProjectIndex > contextProjectIndex) currentProjectIndex--;
            switchProject(currentProjectIndex);
            renderProjectTabs();
        }
        document.getElementById('project-context-menu').style.display = 'none';
    };
}

const renameProjectBtn = document.getElementById('rename-project');
if (renameProjectBtn) {
    renameProjectBtn.onclick = function() {
        renameProject(contextProjectIndex);
        document.getElementById('project-context-menu').style.display = 'none';
    };
}

const duplicateProjectBtn = document.getElementById('duplicate-project');
if (duplicateProjectBtn) {
    duplicateProjectBtn.onclick = function() {
        duplicateProject(contextProjectIndex);
        document.getElementById('project-context-menu').style.display = 'none';
    };
}

const closeProjectBtn = document.getElementById('close-project');
if (closeProjectBtn) {
    closeProjectBtn.onclick = function() {
        closeProject(contextProjectIndex);
        document.getElementById('project-context-menu').style.display = 'none';
    };
}

// 4. 字段区相关操作后自动保存
function onFieldsChanged() {
  saveCurrentProject();
}

// 5. 挂载字段区事件
function mountFieldEvents() {
  const fieldsContainer = document.getElementById('fields-container');
  if (fieldsContainer) {
    const debouncedTest = debounce(testSelector, 300);
    fieldsContainer.addEventListener('input', (e) => {
      onFieldsChanged();
      debouncedTest();
    });
    fieldsContainer.addEventListener('change', (e) => {
      onFieldsChanged();
      debouncedTest();
    });
  }
}

// 保存项目到文件
function saveProjectToFile() {
    saveCurrentProject(); // 确保当前项目已保存
    const projectData = {
        projects: projects,
        currentProjectIndex: currentProjectIndex,
        version: '1.0'
    };

    const dataStr = JSON.stringify(projectData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'scraper-projects.json';
    link.click();

    URL.revokeObjectURL(link.href);
}

// 从文件加载项目
function loadProjectFromFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const projectData = JSON.parse(e.target.result);

            if (projectData.projects && Array.isArray(projectData.projects)) {
                projects = projectData.projects;
                currentProjectIndex = projectData.currentProjectIndex || 0;

                // 确保索引有效
                if (currentProjectIndex >= projects.length) {
                    currentProjectIndex = 0;
                }

                switchProject(currentProjectIndex);
                renderProjectTabs();
                alert('项目加载成功！');
            } else {
                alert('无效的项目文件格式！');
            }
        } catch (error) {
            alert('加载项目文件失败：' + error.message);
        }
    };

    reader.readAsText(file);
    // 清空文件输入，允许重复选择同一文件
    event.target.value = '';
}

// 撤销功能
function saveStateToHistory() {
    if (isRestoring) return; // 避免在恢复时保存状态

    const currentState = {
        projects: JSON.parse(JSON.stringify(projects)),
        currentProjectIndex: currentProjectIndex,
        timestamp: Date.now()
    };

    historyStack.push(currentState);

    // 限制历史记录数量
    if (historyStack.length > 20) {
        historyStack.shift();
    }

    // 更新撤销按钮状态
    const undoBtn = document.getElementById('undo-btn');
    if (undoBtn) {
        undoBtn.disabled = historyStack.length === 0;
    }
}

function undoLastAction() {
    if (historyStack.length === 0) return;

    isRestoring = true;
    const lastState = historyStack.pop();

    projects = lastState.projects;
    currentProjectIndex = lastState.currentProjectIndex;

    switchProject(currentProjectIndex);
    renderProjectTabs();

    // 更新撤销按钮状态
    const undoBtn = document.getElementById('undo-btn');
    if (undoBtn) {
        undoBtn.disabled = historyStack.length === 0;
    }

    isRestoring = false;
}