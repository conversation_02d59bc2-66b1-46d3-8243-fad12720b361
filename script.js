let currentMode = 'multi'; // 默认多字段模式
let currentFramework = 'colly';
let historyStack = [];
let isRestoring = false;

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => { clearTimeout(timeout); func(...args); };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

document.addEventListener('DOMContentLoaded', () => {
    // Theme switcher logic
    const themeToggleButton = document.getElementById('theme-toggle-btn');
    const currentTheme = localStorage.getItem('theme');

    if (currentTheme === 'dark') {
        document.body.classList.add('dark-mode');
    }

    themeToggleButton.addEventListener('click', () => {
        document.body.classList.toggle('dark-mode');
        let theme = 'light';
        if (document.body.classList.contains('dark-mode')) {
            theme = 'dark';
        }
        localStorage.setItem('theme', theme);
    });

    // Modal logic
    const modal = document.getElementById('reference-modal');
    const openBtn = document.getElementById('open-reference-btn');
    const closeBtn = document.getElementById('close-modal-btn');

    openBtn.addEventListener('click', () => modal.classList.add('active'));
    closeBtn.addEventListener('click', () => modal.classList.remove('active'));
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('active');
        }
    });

    document.querySelectorAll('.accordion-header').forEach(header => {
        header.addEventListener('click', () => {
            const content = header.nextElementSibling;
            header.classList.toggle('active');
            if (content.style.maxHeight) {
                content.style.maxHeight = null;
            } else {
                content.style.maxHeight = content.scrollHeight + "px";
            }
        });
    });

    // 设置默认HTML
    document.getElementById('html-input').value = `
<div class="product-list">
    <div class="product-card" data-id="p001">
        <a href="/product/1">
            <img src="/images/product1.jpg" alt="Go语言编程">
            <h2 class="product-title">Go语言并发编程实战</h2>
        </a>
        <div class="price-box">
            <span class="price">¥79.00</span>
            <span class="original-price">¥99.00</span>
        </div>
        <div class="rating">4.8星</div>
    </div>
    <div class="product-card" data-id="p002">
        <a href="/product/2">
            <img src="/images/product2.jpg" alt="Python爬虫">
            <h2 class="product-title">Python网络爬虫权威指南</h2>
        </a>
        <div class="price-box">
            <span class="price">¥88.50</span>
        </div>
        <div class="rating">4.9星</div>
    </div>
    <div class="product-card" data-id="p003">
        <a href="/product/3">
            <img src="/images/product3.jpg" alt="算法导论">
            <h2 class="product-title">算法导论 (原书第3版)</h2>
        </a>
        <div class="price-box">
            <span class="price">¥129.00</span>
        </div>
        <div class="rating">5.0星</div>
    </div>
</div>`;
    
    // 默认设置
    document.getElementById('list-item-selector').value = '.product-card';
    addField('title', 'h2.product-title', 'text');
    addField('price', '.price', 'text');
    addField('url', 'a', 'attribute', 'href');

    // 实时匹配监听
    const debouncedTest = debounce(testSelector, 300);
    document.getElementById('list-item-selector').addEventListener('input', debouncedTest);
    document.getElementById('html-input').addEventListener('input', debouncedTest);
    document.getElementById('fields-container').addEventListener('input', debouncedTest);
    document.getElementById('add-field-btn').addEventListener('click', () => {
        saveStateToHistory(); // 保存状态用于撤销
        addField();
    });

    // 格式化HTML按钮逻辑
    document.getElementById('format-html-btn').onclick = function() {
        const textarea = document.getElementById('html-input');
        try {
            textarea.value = html_beautify(textarea.value, { indent_size: 2, wrap_line_length: 120 });
        } catch (e) {
            alert('格式化失败，请检查HTML内容是否有误！');
        }
    };

    testSelector();
    renderProjectTabs();
    mountFieldEvents();

    // 保存和加载项目功能
    document.getElementById('save-project-btn').addEventListener('click', saveProjectToFile);
    document.getElementById('load-project-btn').addEventListener('click', () => {
        document.getElementById('load-project-file').click();
    });
    document.getElementById('load-project-file').addEventListener('change', loadProjectFromFile);

    // 撤销功能
    document.getElementById('undo-btn').addEventListener('click', undoLastAction);
});

require.config({ paths: { 'vs': 'https://unpkg.com/monaco-editor@0.44.0/min/vs' }});
let monacoEditor;

// 1. 将 goDemoCode 提前到顶部
const goDemoCode = `package main\n\nimport (\n    \"fmt\"\n    \"github.com/gocolly/colly\"\n)\n\ntype ProductItem struct {\n    Url string \`colly:\"selector:a attr:href\"\`\n}\n\nfunc main() {\n    // 实例化 Colly 爬虫\n    c := colly.NewCollector(\n        colly.AllowedDomains(\"example.com\"),\n    )\n\n    // 解析页面元素\n    c.OnHTML(\".product-card\", func(e *colly.HTMLElement) {\n        item := &ProductItem{}\n        err := e.Unmarshal(item)\n        if err != nil {\n            fmt.Println(\"解析失败:\", err)\n            return\n        }\n        fmt.Printf(\"抓取到商品: %+v\\n\", item)\n    })\n\n    c.Visit(\"https://example.com/products\")\n}\n`;

// 1. 工程对象增加 html、selector、fields 字段
let projects = [
  { name: "默认工程", code: goDemoCode, language: "go", html: '', selector: '', fields: [] }
];
let currentProjectIndex = 0;

function renderProjectTabs() {
  const tabs = document.getElementById('project-tabs');
  tabs.innerHTML = '';
  projects.forEach((p, i) => {
    const btn = document.createElement('button');
    btn.className = 'framework-tab' + (i === currentProjectIndex ? ' active' : '');
    btn.textContent = p.name;
    btn.onclick = () => {
      saveCurrentProject();
      switchProject(i);
    };
    tabs.appendChild(btn);
  });
  // 新建工程按钮
  const addBtn = document.createElement('button');
  addBtn.className = 'add-project-tab';
  addBtn.textContent = '+';
  addBtn.title = '新建工程';
  addBtn.onclick = function() {
    saveCurrentProject();
    projects.push({ name: '未命名工程', code: '', language: 'go', html: '', selector: '', fields: [] });
    switchProject(projects.length - 1);
    renderProjectTabs();
  };
  tabs.appendChild(addBtn);
}

// 2. 保存当前工程所有配置
function saveCurrentProject() {
  if (monacoEditor && projects[currentProjectIndex]) {
    const p = projects[currentProjectIndex];
    p.code = monacoEditor.getValue();
    const langSelect = document.getElementById('language-select');
    if (langSelect) {
      p.language = langSelect.value;
    }
    p.html = document.getElementById('html-input').value;
    p.selector = document.getElementById('list-item-selector').value;
    p.fields = getFields();
  }
}

// 3. 切换工程时恢复所有配置
function switchProject(index) {
  currentProjectIndex = index;
  renderProjectTabs();
  const p = projects[index];
  if (monacoEditor) {
    monacoEditor.setValue(p.code || '');
    monaco.editor.setModelLanguage(monacoEditor.getModel(), p.language || 'go');
  }
  const langSelect = document.getElementById('language-select');
  if (langSelect) {
    langSelect.value = p.language || 'go';
  }
  document.getElementById('html-input').value = p.html || '';
  document.getElementById('list-item-selector').value = p.selector || '';
  document.getElementById('fields-container').innerHTML = '';
  if (p.fields && p.fields.length) {
    p.fields.forEach(f => addField(f.name, f.selector, f.type, f.attribute));
  }
  testSelector();
}

require(['vs/editor/editor.main'], function() {
  monacoEditor = monaco.editor.create(document.getElementById('editor'), {
    value: projects[0].code,
    language: projects[0].language,
    theme: 'vs-dark',
    fontSize: 17,
    minimap: { enabled: false }
  });
  // 监听下拉框切换语言
  const langSelect = document.getElementById('language-select');
  if(langSelect) {
    langSelect.addEventListener('change', function() {
      monaco.editor.setModelLanguage(monacoEditor.getModel(), this.value);
      if (projects[currentProjectIndex]) {
        projects[currentProjectIndex].language = this.value;
      }
    });
  }
  // 编辑器内容变动时，自动保存到当前工程
  monacoEditor.onDidChangeModelContent(function() {
    if (projects[currentProjectIndex]) {
      projects[currentProjectIndex].code = monacoEditor.getValue();
    }
  });
  renderProjectTabs();
});

function addField(name = '', selector = '', type = 'text', attr = '') {
    const template = document.getElementById('field-card-template');
    if (!template) {
        console.error('找不到字段模板元素');
        return;
    }
    const clone = template.content.cloneNode(true);
    const fieldCard = clone.querySelector('.field-card');

    if (!fieldCard) {
        console.error('找不到字段卡片元素');
        return;
    }

    // 安全地设置字段值
    const fieldNameInput = fieldCard.querySelector('.field-name');
    const subSelectorInput = fieldCard.querySelector('.sub-selector');
    const extractTypeSelect = fieldCard.querySelector('.extract-type');

    if (fieldNameInput) fieldNameInput.value = name;
    if (subSelectorInput) subSelectorInput.value = selector;
    if (extractTypeSelect) extractTypeSelect.value = type;
    const attrGroup = fieldCard.querySelector('.attr-name-group');
    const attrInput = fieldCard.querySelector('.attr-name');
    if (attrGroup && attrInput) {
        if (type === 'attribute') {
            attrGroup.style.display = 'flex';
            attrInput.value = attr;
        } else {
            attrGroup.style.display = 'none';
        }
    }

    const removeBtn = fieldCard.querySelector('.remove-field-btn');
    if (removeBtn) {
        removeBtn.onclick = () => {
            saveStateToHistory(); // 保存状态用于撤销
            fieldCard.remove();
            saveCurrentProject();
            testSelector();
        };
    }

    if (extractTypeSelect && attrGroup) {
        extractTypeSelect.onchange = (e) => {
            attrGroup.style.display = e.target.value === 'attribute' ? 'flex' : 'none';
            saveCurrentProject();
            testSelector();
        };
    }

    // 为新字段添加输入事件监听器
    if (fieldNameInput) {
        fieldNameInput.addEventListener('input', () => {
            saveCurrentProject();
            testSelector();
        });
    }
    if (subSelectorInput) {
        subSelectorInput.addEventListener('input', () => {
            saveCurrentProject();
            testSelector();
        });
    }
    if (attrInput) {
        attrInput.addEventListener('input', () => {
            saveCurrentProject();
            testSelector();
        });
    }

    // 拖拽逻辑
    fieldCard.addEventListener('dragstart', handleDragStart);
    fieldCard.addEventListener('dragover', handleDragOver);
    fieldCard.addEventListener('dragleave', handleDragLeave);
    fieldCard.addEventListener('drop', handleDrop);
    fieldCard.addEventListener('dragend', handleDragEnd);

    const fieldsContainer = document.getElementById('fields-container');
    if (!fieldsContainer) {
        console.error('找不到字段容器元素');
        return;
    }
    fieldsContainer.appendChild(clone);
    saveCurrentProject();
    testSelector();
}

let draggedItem = null;

function handleDragStart(e) {
    draggedItem = this;
    setTimeout(() => {
        this.classList.add('dragging');
    }, 0);
}

function handleDragOver(e) {
    e.preventDefault();
    const container = document.getElementById('fields-container');
    const afterElement = getDragAfterElement(container, e.clientY);
    if (afterElement == null) {
        container.appendChild(draggedItem);
    } else {
        container.insertBefore(draggedItem, afterElement);
    }
}

function handleDragLeave(e) {
    this.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
}

function handleDragEnd(e) {
    this.classList.remove('dragging');
    testSelector();
}

function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.field-card:not(.dragging)')];

    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}


function clearResults() {
    document.getElementById('results-container').innerHTML = '<div class="no-matches">输入信息后，结果将实时更新</div>';
    // 清空Monaco编辑器中的代码
    if (monacoEditor) {
        monacoEditor.setValue('// 输入信息后，代码将实时更新');
    }
}

function testSelector() {
    testMultiField();
}

function testMultiField() {
    const listItemSelector = document.getElementById('list-item-selector').value;
    const html = document.getElementById('html-input').value;
    const fieldCards = document.querySelectorAll('#fields-container .field-card');
    if (!listItemSelector || !html || fieldCards.length === 0) { clearResults(); return; }
    const fields = getFields();
    if (fields.length === 0) { clearResults(); return; }
    const doc = new DOMParser().parseFromString(html, 'text/html');
    const listItems = doc.querySelectorAll(listItemSelector);
    const resultsData = [];
    listItems.forEach(item => {
        const rowData = {};
        fields.forEach(field => {
            const targetElement = field.selector ? item.querySelector(field.selector) : item;
            if (targetElement) {
                if (field.type === 'text') { rowData[field.name] = targetElement.textContent.trim(); } 
                else if (field.type === 'attribute' && field.attribute) { rowData[field.name] = targetElement.getAttribute(field.attribute); }
            } else { rowData[field.name] = null; }
        });
        resultsData.push(rowData);
    });
    renderMultiFieldResults(resultsData, fields);
    generateCode();
}

function renderMultiFieldResults(data, fields) {
    if (data.length === 0) { document.getElementById('results-container').innerHTML = '<div class="no-matches">未找到匹配的列表项</div>'; return; }
    const table = document.createElement('table');
    table.className = 'results-table';
    const thead = table.createTHead();
    const headerRow = thead.insertRow();
    fields.forEach(field => { const th = document.createElement('th'); th.textContent = field.name; headerRow.appendChild(th); });
    const tbody = table.createTBody();
    data.forEach(rowData => {
        const row = tbody.insertRow();
        fields.forEach(field => { const cell = row.insertCell(); cell.textContent = rowData[field.name] ?? 'N/A'; });
    });
    const container = document.getElementById('results-container');
    container.innerHTML = '';
    container.appendChild(table);
}

function generateCode() {
    const generator = codeGenerators[currentFramework];
    if (generator && monacoEditor) {
        const generatedCode = generator();
        monacoEditor.setValue(generatedCode);
        // 保存到当前项目
        if (projects[currentProjectIndex]) {
            projects[currentProjectIndex].code = generatedCode;
        }
    }
}

const codeGenerators = {
    colly: () => {
        const listItemSelector = document.getElementById('list-item-selector').value;
        const fields = getFields();
        if (!listItemSelector || fields.length === 0) return '<span class="comment-highlight">// 请输入列表项选择器并添加字段</span>';

        const structName = "ProductItem";
        let structFields = "";
        fields.forEach(f => {
            const fieldName = f.name.charAt(0).toUpperCase() + f.name.slice(1);
            let tag = `selector:"${f.selector || "."}"`;
            if (f.type === 'attribute') { tag += ` attr:"${f.attribute}"`; }
            structFields += `    ${fieldName} string \`colly:"${tag}"\`\n`;
        });

        return `package main\n\nimport (\n\t"fmt"\n\t"log"\n\n\t"github.com/gocolly/colly"\n)\n\n// ${structName} represents the structure of a product item\ntype ${structName} struct {\n${structFields}}\n\nfunc main() {\n\t// Instantiate a new Colly collector\n\tc := colly.NewCollector(\n\t\t// Visit only domains: example.com\n\t\tcolly.AllowedDomains("example.com"),\n\t)\n\n\t// On every a element which has href attribute call callback\n\tc.OnHTML("${listItemSelector}", func(e *colly.HTMLElement) {\n\t\titem := &${structName}{}\n\t\t// Unmarshal the HTML element into the struct\n\t\terr := e.Unmarshal(item)\n\t\tif err != nil {\n\t\t\tlog.Printf("Error unmarshaling item: %v", err)\n\t\t\treturn\n\t\t}\n\t\tfmt.Printf("Found Item: %+v\n", item)\n\t})\n\n\t// Start scraping on https://example.com\n\tlog.Println("Visiting https://example.com...")\n\t err := c.Visit("https://example.com")\n\t if err != nil {\n\t\tlog.Fatalf("Error visiting URL: %v", err)\n\t }\n\n\tlog.Println("Scraping finished.")\n}`;
    },
};

function getFields() {
    return Array.from(document.querySelectorAll('#fields-container .field-card')).map(card => ({ 
        name: card.querySelector('.field-name').value.trim(), 
        selector: card.querySelector('.sub-selector').value.trim(), 
        type: card.querySelector('.extract-type').value, 
        attribute: card.querySelector('.attr-name') ? card.querySelector('.attr-name').value.trim() : ''
    })).filter(f => f.name);
}

function switchFramework(framework) {
    currentFramework = framework;
    document.querySelectorAll('.framework-tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');
    generateCode();
}

function copyCode() {
    if (monacoEditor) {
        const text = monacoEditor.getValue();
        navigator.clipboard.writeText(text).then(() => {
            const btn = document.querySelector('.copy-btn');
            if (btn) {
                btn.textContent = '✅ 已复制';
                setTimeout(() => { btn.textContent = '📋 复制代码'; }, 2000);
            }
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动复制代码');
        });
    }
}

function escapeHtml(unsafe) {
    return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
}

// 试用选择器函数 - 用于参考手册中的"试用"按钮
function trySelector(selector) {
    document.getElementById('list-item-selector').value = selector;
    testSelector();
    // 关闭模态窗口
    document.getElementById('reference-modal').classList.remove('active');
}

// 工程管理功能（按钮版）- 移除重复定义

// 右键菜单逻辑
let contextProjectIndex = null;
function showProjectContextMenu(e, projectIndex) {
    const menu = document.getElementById('project-context-menu');
    contextProjectIndex = projectIndex;
    menu.style.display = 'block';
    menu.style.left = e.pageX + 'px';
    menu.style.top = e.pageY + 'px';
}
// 隐藏菜单
window.addEventListener('click', function() {
    document.getElementById('project-context-menu').style.display = 'none';
});
// 删除工程
const deleteProjectBtn = document.getElementById('delete-project');
if (deleteProjectBtn) {
    deleteProjectBtn.onclick = function() {
        if (projects.length === 1) {
            alert('至少保留一个工程！');
            return;
        }
        if (confirm('确定要删除该工程吗？')) {
            saveStateToHistory(); // 保存状态用于撤销
            projects.splice(contextProjectIndex, 1);
            if (currentProjectIndex >= projects.length) currentProjectIndex = projects.length - 1;
            switchProject(currentProjectIndex);
            renderProjectTabs();
        }
        document.getElementById('project-context-menu').style.display = 'none';
    };
}
// 重命名工程
const renameProjectBtn = document.getElementById('rename-project');
if (renameProjectBtn) {
    renameProjectBtn.onclick = function() {
        const newName = prompt('请输入新名称：', projects[contextProjectIndex].name);
        if (newName && newName.trim()) {
            projects[contextProjectIndex].name = newName.trim();
            renderProjectTabs();
        }
        document.getElementById('project-context-menu').style.display = 'none';
    };
}

// 4. 字段区相关操作后自动保存
function onFieldsChanged() {
  saveCurrentProject();
}

// 5. 挂载字段区事件
function mountFieldEvents() {
  document.getElementById('fields-container').addEventListener('input', onFieldsChanged);
  document.getElementById('fields-container').addEventListener('change', onFieldsChanged);
}

// 保存项目到文件
function saveProjectToFile() {
    saveCurrentProject(); // 确保当前项目已保存
    const projectData = {
        projects: projects,
        currentProjectIndex: currentProjectIndex,
        version: '1.0'
    };

    const dataStr = JSON.stringify(projectData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'scraper-projects.json';
    link.click();

    URL.revokeObjectURL(link.href);
}

// 从文件加载项目
function loadProjectFromFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const projectData = JSON.parse(e.target.result);

            if (projectData.projects && Array.isArray(projectData.projects)) {
                projects = projectData.projects;
                currentProjectIndex = projectData.currentProjectIndex || 0;

                // 确保索引有效
                if (currentProjectIndex >= projects.length) {
                    currentProjectIndex = 0;
                }

                switchProject(currentProjectIndex);
                renderProjectTabs();
                alert('项目加载成功！');
            } else {
                alert('无效的项目文件格式！');
            }
        } catch (error) {
            alert('加载项目文件失败：' + error.message);
        }
    };

    reader.readAsText(file);
    // 清空文件输入，允许重复选择同一文件
    event.target.value = '';
}

// 撤销功能
function saveStateToHistory() {
    if (isRestoring) return; // 避免在恢复时保存状态

    const currentState = {
        projects: JSON.parse(JSON.stringify(projects)),
        currentProjectIndex: currentProjectIndex,
        timestamp: Date.now()
    };

    historyStack.push(currentState);

    // 限制历史记录数量
    if (historyStack.length > 20) {
        historyStack.shift();
    }

    // 更新撤销按钮状态
    const undoBtn = document.getElementById('undo-btn');
    if (undoBtn) {
        undoBtn.disabled = historyStack.length === 0;
    }
}

function undoLastAction() {
    if (historyStack.length === 0) return;

    isRestoring = true;
    const lastState = historyStack.pop();

    projects = lastState.projects;
    currentProjectIndex = lastState.currentProjectIndex;

    switchProject(currentProjectIndex);
    renderProjectTabs();

    // 更新撤销按钮状态
    const undoBtn = document.getElementById('undo-btn');
    if (undoBtn) {
        undoBtn.disabled = historyStack.length === 0;
    }

    isRestoring = false;
}