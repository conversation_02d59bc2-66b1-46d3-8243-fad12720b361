<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级爬虫选择器调试工具 - Vue版</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Monaco Editor -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.34.1/min/vs/loader.js"></script>
    <style>
        body { margin: 0; font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif; }
        .monaco-container { height: 300px; border: 1px solid #dcdfe6; border-radius: 4px; margin-top: 12px; }
        .field-card { margin-bottom: 16px; }
        .drag-handle { cursor: move; color: #909399; margin-right: 8px; }
        .drag-handle:hover { color: #409eff; }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 顶部导航栏 -->
            <el-header style="padding: 0; height: 60px;">
                <div style="height: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <h2 style="margin: 0; font-size: 18px; font-weight: 600;">
                            <el-icon style="margin-right: 8px;"><Tools /></el-icon>
                            高级爬虫选择器调试工具
                        </h2>
                        <el-tag v-if="currentProject" type="info" effect="plain" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                            {{ currentProject.name }} ({{ currentProject.fields.length }} 字段)
                        </el-tag>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <el-button type="primary" :icon="Save" @click="saveProject" size="small" plain>保存</el-button>
                        <el-button type="primary" :icon="FolderOpened" @click="loadProject" size="small" plain>加载</el-button>
                        <el-switch v-model="isDarkMode" @change="toggleTheme" active-text="深色" inactive-text="浅色"></el-switch>
                    </div>
                </div>
            </el-header>

            <!-- 项目标签栏 -->
            <div style="background: #f5f7fa; border-bottom: 1px solid #e4e7ed; padding: 0 20px; display: flex; align-items: center; min-height: 48px;">
                <el-tabs v-model="currentProjectIndex" @tab-click="switchProject" @tab-remove="closeProject" type="card" closable>
                    <el-tab-pane 
                        v-for="(project, index) in projects" 
                        :key="index"
                        :label="project.name"
                        :name="index.toString()"
                    >
                        <template #label>
                            <span style="display: flex; align-items: center; gap: 6px;">
                                <el-icon><Document /></el-icon>
                                {{ project.name }}
                                <el-badge v-if="project.fields.length > 0" :value="project.fields.length" type="primary" />
                            </span>
                        </template>
                    </el-tab-pane>
                </el-tabs>
                <el-button type="primary" :icon="Plus" @click="createNewProject" size="small" style="margin-left: 12px;">新建</el-button>
            </div>

            <!-- 主要内容区域 -->
            <el-container>
                <!-- 左侧配置面板 -->
                <el-aside width="380px" style="border-right: 1px solid #e4e7ed;">
                    <div style="padding: 16px 20px; border-bottom: 1px solid #e4e7ed; background: #fafafa; font-weight: 600; color: #303133; display: flex; align-items: center; gap: 8px;">
                        <el-icon><Setting /></el-icon>
                        配置面板
                    </div>
                    <div style="padding: 20px; overflow-y: auto; height: calc(100vh - 168px);">
                        <el-form label-position="top">
                            <!-- HTML输入 -->
                            <el-form-item label="HTML代码">
                                <el-input 
                                    v-model="currentProject.html"
                                    type="textarea" 
                                    :rows="6"
                                    placeholder="请输入要解析的HTML代码..."
                                    @input="onHtmlChange"
                                />
                            </el-form-item>

                            <!-- 列表项选择器 -->
                            <el-form-item>
                                <template #label>
                                    <span style="display: flex; align-items: center; gap: 8px;">
                                        列表项选择器
                                        <el-button type="primary" :icon="QuestionFilled" @click="showReference" size="small" text>参考</el-button>
                                    </span>
                                </template>
                                <el-input 
                                    v-model="currentProject.selector"
                                    placeholder="例如: .product-card"
                                    @input="onSelectorChange"
                                >
                                    <template #prefix>
                                        <el-icon><Search /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>

                            <!-- 字段配置 -->
                            <el-form-item label="提取字段">
                                <div style="margin-bottom: 12px;">
                                    <el-button type="primary" :icon="Plus" @click="addField" size="small">添加字段</el-button>
                                    <el-button-group style="margin-left: 12px;">
                                        <el-button :icon="Document" @click="addPresetField('title')" size="small">标题</el-button>
                                        <el-button :icon="Link" @click="addPresetField('link')" size="small">链接</el-button>
                                        <el-button :icon="Picture" @click="addPresetField('image')" size="small">图片</el-button>
                                    </el-button-group>
                                </div>

                                <!-- 字段列表 -->
                                <div v-if="currentProject.fields.length === 0" style="text-align: center; color: #909399; padding: 40px 0;">
                                    <el-icon size="48" style="margin-bottom: 16px;"><Box /></el-icon>
                                    <p>暂无字段，点击上方按钮添加</p>
                                </div>

                                <el-card v-for="(field, index) in currentProject.fields" :key="index" class="field-card" shadow="hover">
                                    <div style="display: flex; align-items: flex-start; gap: 12px;">
                                        <el-icon class="drag-handle"><Rank /></el-icon>
                                        <div style="flex: 1;">
                                            <el-row :gutter="12">
                                                <el-col :span="12">
                                                    <el-form-item label="字段名" size="small">
                                                        <el-input v-model="field.name" placeholder="例如: title" @input="onFieldChange" />
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-form-item label="提取类型" size="small">
                                                        <el-select v-model="field.type" @change="onFieldChange">
                                                            <el-option label="文本" value="text" />
                                                            <el-option label="属性" value="attribute" />
                                                        </el-select>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                            <el-row :gutter="12">
                                                <el-col :span="field.type === 'attribute' ? 12 : 24">
                                                    <el-form-item label="子选择器" size="small">
                                                        <el-input v-model="field.selector" placeholder="例如: h2.name" @input="onFieldChange" />
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="12" v-if="field.type === 'attribute'">
                                                    <el-form-item label="属性名" size="small">
                                                        <el-input v-model="field.attribute" placeholder="例如: href" @input="onFieldChange" />
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                        </div>
                                        <el-button type="danger" :icon="Delete" @click="removeField(index)" size="small" circle />
                                    </div>
                                </el-card>
                            </el-form-item>

                            <!-- 测试按钮 -->
                            <el-button type="success" :icon="Search" @click="testSelector" style="width: 100%;" size="large">
                                测试选择器
                            </el-button>
                        </el-form>
                    </div>
                </el-aside>

                <!-- 中间结果面板 -->
                <el-main>
                    <div style="padding: 16px 20px; border-bottom: 1px solid #e4e7ed; background: #fafafa; font-weight: 600; color: #303133; display: flex; align-items: center; gap: 8px;">
                        <el-icon><DataAnalysis /></el-icon>
                        匹配结果
                    </div>
                    <div style="padding: 20px;">
                        <!-- 统计信息 -->
                        <el-card style="margin-bottom: 16px;" v-if="results.length > 0">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; gap: 24px;">
                                    <div style="text-align: center;">
                                        <div style="font-size: 24px; font-weight: bold; color: #409eff;">{{ results.length }}</div>
                                        <div style="color: #909399; font-size: 12px;">匹配项</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 24px; font-weight: bold; color: #67c23a;">{{ currentProject.fields.length }}</div>
                                        <div style="color: #909399; font-size: 12px;">字段数</div>
                                    </div>
                                </div>
                                <el-button type="primary" :icon="Download" @click="exportResults" size="small">导出数据</el-button>
                            </div>
                        </el-card>

                        <!-- 结果表格 -->
                        <div style="max-height: 400px; overflow-y: auto;">
                            <el-empty v-if="results.length === 0" description="暂无匹配结果">
                                <template #image>
                                    <el-icon size="60" style="color: #c0c4cc;"><Search /></el-icon>
                                </template>
                            </el-empty>

                            <el-table v-else :data="results" stripe style="width: 100%;" size="small">
                                <el-table-column 
                                    v-for="field in currentProject.fields" 
                                    :key="field.name"
                                    :prop="field.name" 
                                    :label="field.name"
                                    show-overflow-tooltip
                                />
                            </el-table>
                        </div>
                    </div>
                </el-main>

                <!-- 右侧代码面板 -->
                <el-aside width="450px" style="border-left: 1px solid #e4e7ed;">
                    <div style="padding: 16px 20px; border-bottom: 1px solid #e4e7ed; background: #fafafa; font-weight: 600; color: #303133; display: flex; align-items: center; gap: 8px;">
                        <el-icon><Code /></el-icon>
                        生成代码
                        <div style="margin-left: auto; display: flex; gap: 8px;">
                            <el-select v-model="codeLanguage" size="small" style="width: 100px;">
                                <el-option label="Go" value="go" />
                                <el-option label="Python" value="python" />
                            </el-select>
                            <el-button type="primary" :icon="DocumentCopy" @click="copyCode" size="small">复制</el-button>
                        </div>
                    </div>
                    <div style="padding: 20px;">
                        <div class="monaco-container" ref="monacoContainer"></div>
                    </div>
                </el-aside>
            </el-container>
        </el-container>

        <!-- 隐藏的文件输入 -->
        <input type="file" ref="fileInput" @change="handleFileLoad" accept=".json" style="display: none;" />

        <!-- CSS选择器参考手册对话框 -->
        <el-dialog v-model="showReferenceDialog" title="CSS选择器参考手册" width="70%">
            <el-collapse>
                <el-collapse-item title="基础选择器" name="basic">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="元素选择器">
                            <code>div</code> - 选择所有div元素
                            <el-button type="primary" size="small" @click="trySelector('div')" style="margin-left: 12px;">试用</el-button>
                        </el-descriptions-item>
                        <el-descriptions-item label="类选择器">
                            <code>.class-name</code> - 选择指定类名的元素
                            <el-button type="primary" size="small" @click="trySelector('.product-card')" style="margin-left: 12px;">试用</el-button>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-collapse-item>
            </el-collapse>
        </el-dialog>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed, watch, onMounted, nextTick } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                // 响应式数据
                const isDarkMode = ref(false);
                const currentProjectIndex = ref('0');
                const codeLanguage = ref('go');
                const showReferenceDialog = ref(false);
                const results = ref([]);
                const monacoEditor = ref(null);

                // 项目数据
                const projects = ref([
                    {
                        name: '🚀 主项目',
                        html: '<div class="product-card"><h2 class="product-title">示例商品</h2><span class="price">￥99</span><a href="/product/1">查看详情</a></div>',
                        selector: '.product-card',
                        fields: [
                            { name: 'title', selector: 'h2.product-title', type: 'text', attribute: '' },
                            { name: 'price', selector: '.price', type: 'text', attribute: '' },
                            { name: 'url', selector: 'a', type: 'attribute', attribute: 'href' }
                        ],
                        hasUnsavedChanges: false
                    }
                ]);

                // 计算属性
                const currentProject = computed(() => {
                    const index = parseInt(currentProjectIndex.value);
                    return projects.value[index] || projects.value[0];
                });

                // 方法
                const toggleTheme = () => {
                    document.documentElement.classList.toggle('dark', isDarkMode.value);
                };

                const createNewProject = () => {
                    const newProject = {
                        name: `📄 项目 ${projects.value.length + 1}`,
                        html: '',
                        selector: '',
                        fields: [],
                        hasUnsavedChanges: false
                    };
                    projects.value.push(newProject);
                    currentProjectIndex.value = (projects.value.length - 1).toString();
                    ElMessage.success('新项目创建成功');
                };

                const switchProject = (tab) => {
                    currentProjectIndex.value = tab.name;
                    testSelector();
                };

                const closeProject = (name) => {
                    const index = parseInt(name);
                    if (projects.value.length <= 1) {
                        ElMessage.warning('至少需要保留一个项目');
                        return;
                    }
                    projects.value.splice(index, 1);
                    if (parseInt(currentProjectIndex.value) >= projects.value.length) {
                        currentProjectIndex.value = (projects.value.length - 1).toString();
                    }
                    ElMessage.success('项目已关闭');
                };

                const addField = () => {
                    currentProject.value.fields.push({
                        name: '',
                        selector: '',
                        type: 'text',
                        attribute: ''
                    });
                };

                const addPresetField = (type) => {
                    const presets = {
                        title: { name: 'title', selector: 'h1, h2, h3', type: 'text' },
                        link: { name: 'link', selector: 'a', type: 'attribute', attribute: 'href' },
                        image: { name: 'image', selector: 'img', type: 'attribute', attribute: 'src' }
                    };
                    const preset = presets[type];
                    if (preset) {
                        currentProject.value.fields.push({ ...preset, attribute: preset.attribute || '' });
                        ElMessage.success(`${preset.name} 字段已添加`);
                    }
                };

                const removeField = (index) => {
                    currentProject.value.fields.splice(index, 1);
                    testSelector();
                };

                const onHtmlChange = () => {
                    testSelector();
                };

                const onSelectorChange = () => {
                    testSelector();
                };

                const onFieldChange = () => {
                    testSelector();
                };

                const testSelector = () => {
                    if (!currentProject.value.html || !currentProject.value.selector) {
                        results.value = [];
                        generateCode();
                        return;
                    }

                    try {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(currentProject.value.html, 'text/html');
                        const elements = doc.querySelectorAll(currentProject.value.selector);

                        const extractedData = [];
                        elements.forEach(element => {
                            const item = {};
                            currentProject.value.fields.forEach(field => {
                                if (field.name && field.selector) {
                                    const targetElement = element.querySelector(field.selector);
                                    if (targetElement) {
                                        if (field.type === 'attribute' && field.attribute) {
                                            item[field.name] = targetElement.getAttribute(field.attribute) || '';
                                        } else {
                                            item[field.name] = targetElement.textContent.trim();
                                        }
                                    } else {
                                        item[field.name] = '';
                                    }
                                }
                            });
                            extractedData.push(item);
                        });

                        results.value = extractedData;
                        generateCode();

                        if (extractedData.length > 0) {
                            ElMessage.success(`找到 ${extractedData.length} 个匹配项`);
                        }
                    } catch (error) {
                        console.error('解析错误:', error);
                        ElMessage.error('HTML解析失败');
                        results.value = [];
                    }
                };

                const generateCode = () => {
                    if (!monacoEditor.value) return;

                    const selector = currentProject.value.selector;
                    const fields = currentProject.value.fields;

                    if (!selector || fields.length === 0) {
                        monacoEditor.value.setValue('// 请配置选择器和字段后生成代码');
                        return;
                    }

                    let code = '';
                    if (codeLanguage.value === 'go') {
                        code = generateGoCode(selector, fields);
                    } else if (codeLanguage.value === 'python') {
                        code = generatePythonCode(selector, fields);
                    }

                    monacoEditor.value.setValue(code);
                };

                const generateGoCode = (selector, fields) => {
                    const structFields = fields.map(f => {
                        const fieldName = f.name.charAt(0).toUpperCase() + f.name.slice(1);
                        return `\t${fieldName} string // ${f.name}`;
                    }).join('\n');

                    const extractionCode = fields.map(f => {
                        if (f.type === 'attribute') {
                            return `\t\t// 提取${f.name}\n\t\t${f.name} := e.DOM.Find("${f.selector}").AttrOr("${f.attribute}", "")`;
                        } else {
                            return `\t\t// 提取${f.name}\n\t\t${f.name} := e.DOM.Find("${f.selector}").Text()`;
                        }
                    }).join('\n\n');

                    const assignmentCode = fields.map(f => {
                        const fieldName = f.name.charAt(0).toUpperCase() + f.name.slice(1);
                        return `\t\t\t${fieldName}: ${f.name},`;
                    }).join('\n');

                    return `package main

import (
\t"fmt"
\t"log"

\t"github.com/gocolly/colly"
)

type DataItem struct {
${structFields}
}

func main() {
\tc := colly.NewCollector(
\t\tcolly.AllowedDomains("example.com"),
\t\tcolly.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
\t)

\tc.OnHTML("${selector}", func(e *colly.HTMLElement) {
${extractionCode}

\t\titem := &DataItem{
${assignmentCode}
\t\t}

\t\tfmt.Printf("Found Item: %+v\\n", item)
\t})

\tc.OnError(func(r *colly.Response, err error) {
\t\tfmt.Println("请求错误:", err)
\t})

\tlog.Println("正在访问目标网站...")
\terr := c.Visit("https://example.com")
\tif err != nil {
\t\tlog.Fatalf("Error visiting URL: %v", err)
\t}

\tlog.Println("爬取完成")
}`;
                };

                const generatePythonCode = (selector, fields) => {
                    const extractionCode = fields.map(f => {
                        if (f.type === 'attribute') {
                            return `        ${f.name} = item.css('${f.selector}::attr(${f.attribute})').get() or ''`;
                        } else {
                            return `        ${f.name} = item.css('${f.selector}::text').get() or ''`;
                        }
                    }).join('\n');

                    return `import scrapy

class DataSpider(scrapy.Spider):
    name = 'data_spider'
    allowed_domains = ['example.com']
    start_urls = ['https://example.com']

    def parse(self, response):
        items = response.css('${selector}')

        for item in items:
${extractionCode}

            yield {
${fields.map(f => `                '${f.name}': ${f.name},`).join('\n')}
            }`;
                };

                const copyCode = async () => {
                    if (monacoEditor.value) {
                        const code = monacoEditor.value.getValue();
                        try {
                            await navigator.clipboard.writeText(code);
                            ElMessage.success('代码已复制到剪贴板');
                        } catch (err) {
                            ElMessage.error('复制失败');
                        }
                    }
                };

                const exportResults = () => {
                    if (results.value.length === 0) {
                        ElMessage.warning('暂无数据可导出');
                        return;
                    }

                    const csv = [
                        currentProject.value.fields.map(f => f.name).join(','),
                        ...results.value.map(row =>
                            currentProject.value.fields.map(f => `"${(row[f.name] || '').replace(/"/g, '""')}"`).join(',')
                        )
                    ].join('\n');

                    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${currentProject.value.name}_results.csv`;
                    a.click();
                    URL.revokeObjectURL(url);

                    ElMessage.success('数据已导出为CSV文件');
                };

                const saveProject = () => {
                    const data = JSON.stringify(projects.value, null, 2);
                    const blob = new Blob([data], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'scraper_projects.json';
                    a.click();
                    URL.revokeObjectURL(url);

                    ElMessage.success('项目已保存');
                };

                const loadProject = () => {
                    document.querySelector('input[type="file"]').click();
                };

                const handleFileLoad = (event) => {
                    const file = event.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            try {
                                const data = JSON.parse(e.target.result);
                                projects.value = data;
                                currentProjectIndex.value = '0';
                                ElMessage.success('项目加载成功');
                            } catch (error) {
                                ElMessage.error('文件格式错误');
                            }
                        };
                        reader.readAsText(file);
                    }
                };

                const showReference = () => {
                    showReferenceDialog.value = true;
                };

                const trySelector = (selector) => {
                    currentProject.value.selector = selector;
                    testSelector();
                    showReferenceDialog.value = false;
                    ElMessage.success(`已应用选择器: ${selector}`);
                };

                // 监听语言变化
                watch(codeLanguage, () => {
                    generateCode();
                });

                // 组件挂载后初始化Monaco Editor
                onMounted(async () => {
                    await nextTick();

                    // 初始化Monaco Editor
                    require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.34.1/min/vs' } });
                    require(['vs/editor/editor.main'], () => {
                        const container = document.querySelector('.monaco-container');
                        if (container) {
                            monacoEditor.value = monaco.editor.create(container, {
                                value: '// 请配置选择器和字段后生成代码',
                                language: 'go',
                                theme: 'vs-dark',
                                fontSize: 14,
                                minimap: { enabled: false },
                                scrollBeyondLastLine: false,
                                automaticLayout: true
                            });

                            // 初始生成代码
                            setTimeout(() => {
                                testSelector();
                            }, 1000);
                        }
                    });
                });

                return {
                    // 响应式数据
                    isDarkMode,
                    currentProjectIndex,
                    codeLanguage,
                    showReferenceDialog,
                    results,
                    projects,
                    currentProject,

                    // 方法
                    toggleTheme,
                    createNewProject,
                    switchProject,
                    closeProject,
                    addField,
                    addPresetField,
                    removeField,
                    onHtmlChange,
                    onSelectorChange,
                    onFieldChange,
                    testSelector,
                    copyCode,
                    exportResults,
                    saveProject,
                    loadProject,
                    handleFileLoad,
                    showReference,
                    trySelector
                };
            }
        });

        // 注册所有图标组件
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus);
        app.mount('#app');
    </script>
