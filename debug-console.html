<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-panel { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #log { height: 300px; overflow-y: auto; background: #fff; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>主应用程序调试工具</h1>
    
    <div class="debug-panel">
        <h3>快速测试</h3>
        <button onclick="testAddFieldButton()">测试添加字段按钮</button>
        <button onclick="testTemplate()">测试字段模板</button>
        <button onclick="testContainer()">测试字段容器</button>
        <button onclick="testAddFieldFunction()">测试addField函数</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="debug-panel">
        <h3>实时日志</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testAddFieldButton() {
            log('=== 测试添加字段按钮 ===');
            
            // 尝试在主窗口中查找按钮
            try {
                const mainWindow = window.opener || window.parent || window;
                const button = mainWindow.document.getElementById('add-field-btn');
                
                if (button) {
                    log('✓ 找到添加字段按钮', 'success');
                    log(`按钮文本: "${button.textContent}"`);
                    log(`按钮类名: "${button.className}"`);
                    log(`按钮是否可见: ${button.offsetParent !== null}`);
                    log(`按钮是否禁用: ${button.disabled}`);
                    
                    // 检查事件监听器
                    const events = getEventListeners ? getEventListeners(button) : 'getEventListeners不可用';
                    log(`事件监听器: ${JSON.stringify(events)}`);
                    
                    // 尝试手动触发点击
                    log('尝试手动触发点击事件...');
                    button.click();
                    log('点击事件已触发');
                } else {
                    log('✗ 找不到添加字段按钮', 'error');
                }
            } catch (error) {
                log(`错误: ${error.message}`, 'error');
            }
        }
        
        function testTemplate() {
            log('=== 测试字段模板 ===');
            
            try {
                const mainWindow = window.opener || window.parent || window;
                const template = mainWindow.document.getElementById('field-card-template');
                
                if (template) {
                    log('✓ 找到字段模板', 'success');
                    log(`模板类型: ${template.tagName}`);
                    log(`模板内容长度: ${template.innerHTML.length}`);
                    
                    const content = template.content;
                    if (content) {
                        log('✓ 模板内容存在', 'success');
                        const fieldCard = content.querySelector('.field-card');
                        if (fieldCard) {
                            log('✓ 找到字段卡片元素', 'success');
                        } else {
                            log('✗ 找不到字段卡片元素', 'error');
                        }
                    } else {
                        log('✗ 模板内容不存在', 'error');
                    }
                } else {
                    log('✗ 找不到字段模板', 'error');
                }
            } catch (error) {
                log(`错误: ${error.message}`, 'error');
            }
        }
        
        function testContainer() {
            log('=== 测试字段容器 ===');
            
            try {
                const mainWindow = window.opener || window.parent || window;
                const container = mainWindow.document.getElementById('fields-container');
                
                if (container) {
                    log('✓ 找到字段容器', 'success');
                    log(`容器子元素数量: ${container.children.length}`);
                    log(`容器类名: "${container.className}"`);
                    log(`容器是否可见: ${container.offsetParent !== null}`);
                    
                    // 列出所有子元素
                    for (let i = 0; i < container.children.length; i++) {
                        const child = container.children[i];
                        log(`子元素 ${i}: ${child.tagName}.${child.className}`);
                    }
                } else {
                    log('✗ 找不到字段容器', 'error');
                }
            } catch (error) {
                log(`错误: ${error.message}`, 'error');
            }
        }
        
        function testAddFieldFunction() {
            log('=== 测试addField函数 ===');
            
            try {
                const mainWindow = window.opener || window.parent || window;
                
                if (typeof mainWindow.addField === 'function') {
                    log('✓ 找到addField函数', 'success');
                    
                    // 尝试调用函数
                    log('尝试调用addField函数...');
                    mainWindow.addField('测试字段', '.test-selector', 'text');
                    log('addField函数调用完成');
                    
                    // 检查容器是否有新元素
                    const container = mainWindow.document.getElementById('fields-container');
                    if (container) {
                        log(`调用后容器子元素数量: ${container.children.length}`);
                    }
                } else {
                    log('✗ addField函数不存在或不是函数', 'error');
                    log(`addField类型: ${typeof mainWindow.addField}`);
                }
            } catch (error) {
                log(`错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动运行基本测试
        window.addEventListener('load', () => {
            log('调试工具已加载');
            log('请在主应用程序页面打开后使用这些测试按钮');
            log('确保主应用程序在另一个标签页中打开');
        });
    </script>
</body>
</html>
